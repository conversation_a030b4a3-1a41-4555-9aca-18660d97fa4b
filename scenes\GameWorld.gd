# GameWorld.gd
# 游戏世界主场景
extends Node2D

# 节点引用
@onready var time_label = $UI/HUD/TopPanel/TimeLabel
@onready var npcs_container = $World/NPCs
@onready var buildings_container = $World/Buildings

# 游戏状态
var is_paused: bool = false

func _ready():
	"""初始化游戏世界"""
	print("[GameWorld] 游戏世界初始化")
	
	# 设置游戏状态
	if GameDataManager.instance:
		GameDataManager.instance.set_game_state(GameDataManager.GameState.PLAYING)
		
		# 连接时间系统信号
		if GameDataManager.instance.time_system:
			GameDataManager.instance.time_system.hour_passed.connect(_on_hour_passed)
			GameDataManager.instance.time_system.day_passed.connect(_on_day_passed)
		
		# 初始化世界数据
		initialize_world()
	
	# 更新UI
	update_time_display()

func initialize_world():
	"""初始化世界"""
	print("[GameWorld] 初始化世界数据")
	
	# 如果没有世界数据，创建默认世界
	if not GameDataManager.instance.world_data:
		GameDataManager.instance.world_data = WorldData.new()
		GameDataManager.instance.world_data.create_default_world()
	
	# 生成一些测试NPC
	spawn_test_npcs()

func spawn_test_npcs():
	"""生成测试NPC"""
	print("[GameWorld] 生成测试NPC")
	
	var npc_manager = GameDataManager.instance.get_npc_manager()
	if npc_manager:
		# 为默认位置生成一些NPC
		npc_manager.spawn_npcs_for_location("default_city", 5)

func _on_hour_passed(hour: int):
	"""小时变化回调"""
	update_time_display()

func _on_day_passed(day: int):
	"""天数变化回调"""
	print("[GameWorld] 新的一天: 第%d天" % day)

func update_time_display():
	"""更新时间显示"""
	if GameDataManager.instance and GameDataManager.instance.time_system:
		var time_data = GameDataManager.instance.time_system.get_time_data()
		time_label.text = "时间: %s" % time_data.time_string

func _on_menu_button_pressed():
	"""菜单按钮点击"""
	print("[GameWorld] 打开菜单")
	toggle_pause()

func toggle_pause():
	"""切换暂停状态"""
	is_paused = !is_paused
	
	if GameDataManager.instance and GameDataManager.instance.time_system:
		if is_paused:
			GameDataManager.instance.time_system.pause_time()
		else:
			GameDataManager.instance.time_system.resume_time()

func _input(event):
	"""处理输入"""
	if event.is_action_pressed("ui_cancel"):
		# ESC键暂停游戏
		toggle_pause()
	elif event.is_action_pressed("ui_accept"):
		# 空格键加速时间
		if GameDataManager.instance and GameDataManager.instance.time_system:
			var current_scale = GameDataManager.instance.time_system.time_scale
			var new_scale = 2.0 if current_scale == 1.0 else 1.0
			GameDataManager.instance.time_system.set_time_scale(new_scale)

func _exit_tree():
	"""清理资源"""
	print("[GameWorld] 游戏世界清理完成")
