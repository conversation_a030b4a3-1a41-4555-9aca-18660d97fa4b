# Policy.gd
# 政策类 - 表示游戏中的政策和法规
class_name Policy
extends Resource

# 政策类型枚举
enum PolicyType {
	ECONOMIC,       # 经济政策
	SOCIAL,         # 社会政策
	INFRASTRUCTURE, # 基础设施政策
	EDUCATION,      # 教育政策
	HEALTHCARE,     # 医疗政策
	ENVIRONMENTAL,  # 环境政策
	MILITARY,       # 军事政策
	TAX,           # 税收政策
	WELFARE,       # 福利政策
	TRADE          # 贸易政策
}

# 政策影响范围枚举
enum PolicyScope {
	LOCAL,      # 地方政策
	REGIONAL,   # 区域政策
	NATIONAL,   # 国家政策
	GLOBAL      # 全球政策
}

# 基础信息
@export var id: String = ""
@export var name: String = ""
@export var description: String = ""
@export var type: PolicyType = PolicyType.ECONOMIC
@export var scope: PolicyScope = PolicyScope.LOCAL

# 政策参数
@export var effect_value: float = 0.0      # 政策效果值
@export var implementation_cost: float = 0.0 # 实施成本
@export var maintenance_cost: float = 0.0   # 维护成本
@export var duration: int = -1              # 持续时间（天，-1表示永久）

# 实施条件
@export var required_approval: float = 50.0  # 需要的支持率
@export var required_budget: float = 0.0     # 需要的预算
@export var prerequisites: Array[String] = [] # 前置条件

# 状态信息
@export var is_active: bool = false
@export var approval_rating: float = 50.0    # 支持率
@export var implementation_date: String = ""
@export var expiry_date: String = ""

# 影响目标
@export var target_regions: Array[String] = []  # 影响的地区
@export var affected_groups: Array[String] = [] # 影响的群体

func _init():
	"""初始化政策"""
	id = generate_unique_id()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "policy_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func can_implement() -> bool:
	"""检查是否可以实施"""
	# 检查支持率
	if approval_rating < required_approval:
		return false
	
	# 检查预算（这里需要从游戏管理器获取当前预算）
	# if current_budget < required_budget:
	#     return false
	
	# 检查前置条件
	for prerequisite in prerequisites:
		if not check_prerequisite(prerequisite):
			return false
	
	return true

func check_prerequisite(prerequisite: String) -> bool:
	"""检查前置条件"""
	# 这里需要实现具体的前置条件检查逻辑
	return true

func implement() -> bool:
	"""实施政策"""
	if not can_implement():
		return false
	
	is_active = true
	implementation_date = Time.get_datetime_string_from_system()
	
	# 设置过期时间
	if duration > 0:
		var current_time = Time.get_datetime_dict_from_system()
		# 这里需要计算过期日期
		expiry_date = calculate_expiry_date(current_time, duration)
	
	print("[Policy] 政策 '%s' 已实施" % name)
	return true

func calculate_expiry_date(current_time: Dictionary, days: int) -> String:
	"""计算过期日期"""
	# 简化实现，实际应该考虑日历计算
	return Time.get_datetime_string_from_system()

func revoke():
	"""撤销政策"""
	is_active = false
	expiry_date = Time.get_datetime_string_from_system()
	print("[Policy] 政策 '%s' 已撤销" % name)

func update_approval(delta: float):
	"""更新支持率"""
	# 根据政策效果和时间调整支持率
	var approval_change = 0.0
	
	# 正面效果增加支持率
	if effect_value > 0:
		approval_change += effect_value * 0.1
	else:
		approval_change += effect_value * 0.2  # 负面效果影响更大
	
	# 实施成本影响支持率
	if implementation_cost > 10000:
		approval_change -= 5.0
	
	approval_rating = clamp(approval_rating + approval_change * delta, 0.0, 100.0)

func get_policy_type_name() -> String:
	"""获取政策类型名称"""
	match type:
		PolicyType.ECONOMIC:
			return "经济政策"
		PolicyType.SOCIAL:
			return "社会政策"
		PolicyType.INFRASTRUCTURE:
			return "基础设施政策"
		PolicyType.EDUCATION:
			return "教育政策"
		PolicyType.HEALTHCARE:
			return "医疗政策"
		PolicyType.ENVIRONMENTAL:
			return "环境政策"
		PolicyType.MILITARY:
			return "军事政策"
		PolicyType.TAX:
			return "税收政策"
		PolicyType.WELFARE:
			return "福利政策"
		PolicyType.TRADE:
			return "贸易政策"
		_:
			return "未知政策"

func get_policy_scope_name() -> String:
	"""获取政策范围名称"""
	match scope:
		PolicyScope.LOCAL:
			return "地方"
		PolicyScope.REGIONAL:
			return "区域"
		PolicyScope.NATIONAL:
			return "国家"
		PolicyScope.GLOBAL:
			return "全球"
		_:
			return "未知"

func get_status_text() -> String:
	"""获取状态文本"""
	if is_active:
		if duration > 0:
			return "生效中（有期限）"
		else:
			return "生效中（永久）"
	else:
		return "未生效"

func get_policy_summary() -> Dictionary:
	"""获取政策摘要"""
	return {
		"name": name,
		"type": get_policy_type_name(),
		"scope": get_policy_scope_name(),
		"status": get_status_text(),
		"approval_rating": approval_rating,
		"effect_value": effect_value,
		"implementation_cost": implementation_cost,
		"is_active": is_active
	}

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"description": description,
		"type": type,
		"scope": scope,
		"effect_value": effect_value,
		"implementation_cost": implementation_cost,
		"maintenance_cost": maintenance_cost,
		"duration": duration,
		"is_active": is_active,
		"approval_rating": approval_rating,
		"implementation_date": implementation_date,
		"expiry_date": expiry_date
	}

# 预定义政策模板
static func create_tax_policy(tax_rate: float) -> Policy:
	"""创建税收政策"""
	var policy = Policy.new()
	policy.name = "税率调整"
	policy.description = "调整税收税率为 %.1f%%" % (tax_rate * 100)
	policy.type = PolicyType.TAX
	policy.effect_value = tax_rate - 0.15  # 相对于基础税率15%的变化
	policy.implementation_cost = 5000.0
	policy.required_approval = 40.0
	return policy

static func create_welfare_policy(welfare_amount: float) -> Policy:
	"""创建福利政策"""
	var policy = Policy.new()
	policy.name = "社会福利"
	policy.description = "提供社会福利，每人每月 %.0f 元" % welfare_amount
	policy.type = PolicyType.WELFARE
	policy.effect_value = welfare_amount / 1000.0  # 标准化效果值
	policy.implementation_cost = welfare_amount * 1000  # 假设1000人受益
	policy.maintenance_cost = welfare_amount * 1000
	policy.required_approval = 60.0
	return policy

static func create_infrastructure_policy(investment: float) -> Policy:
	"""创建基础设施政策"""
	var policy = Policy.new()
	policy.name = "基础设施建设"
	policy.description = "投资 %.0f 万元用于基础设施建设" % (investment / 10000)
	policy.type = PolicyType.INFRASTRUCTURE
	policy.effect_value = investment / 100000.0  # 标准化效果值
	policy.implementation_cost = investment
	policy.duration = 365  # 一年期
	policy.required_approval = 55.0
	return policy
