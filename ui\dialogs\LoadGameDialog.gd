# LoadGameDialog.gd
# 加载游戏对话框
extends AcceptDialog

# 信号定义
signal game_loaded(save_name: String)

# 节点引用
@onready var save_list = $VBoxContainer/SaveListContainer/SaveList
@onready var save_info_text = $VBoxContainer/SaveInfoContainer/SaveInfoText
@onready var delete_button = $VBoxContainer/ButtonContainer/DeleteButton

# 存档数据
var save_data: Array[Dictionary] = []
var selected_save_index: int = -1

func _ready():
	"""初始化对话框"""
	connect_signals()
	refresh_save_list()

func connect_signals():
	"""连接信号"""
	confirmed.connect(_on_confirmed)
	canceled.connect(_on_canceled)

func show_dialog():
	"""显示对话框"""
	refresh_save_list()
	popup_centered()

func refresh_save_list():
	"""刷新存档列表"""
	print("[LoadGameDialog] 刷新存档列表")
	
	# 清空列表
	save_list.clear()
	save_data.clear()
	selected_save_index = -1
	
	# 获取存档列表
	var save_system = SaveSystem.new()
	save_data = save_system.get_save_list()
	
	# 添加到UI列表
	for save_info in save_data:
		var display_text = "%s - %s" % [save_info.name, save_info.save_time]
		save_list.add_item(display_text)
	
	# 更新UI状态
	update_ui_state()
	
	print("[LoadGameDialog] 找到 %d 个存档" % save_data.size())

func update_ui_state():
	"""更新UI状态"""
	var has_saves = save_data.size() > 0
	var has_selection = selected_save_index >= 0
	
	# 更新确认按钮
	get_ok_button().disabled = not has_selection
	
	# 更新删除按钮
	delete_button.disabled = not has_selection
	
	# 更新信息显示
	if not has_saves:
		save_info_text.text = "[color=gray]没有找到存档文件[/color]"
	elif not has_selection:
		save_info_text.text = "[color=gray]请选择一个存档查看详细信息[/color]"

func _on_save_selected(index: int):
	"""存档选择回调"""
	selected_save_index = index
	update_save_info()
	update_ui_state()

func update_save_info():
	"""更新存档信息显示"""
	if selected_save_index < 0 or selected_save_index >= save_data.size():
		return
	
	var save_info = save_data[selected_save_index]
	
	var info_text = "[b]存档名称:[/b] %s\n" % save_info.name
	info_text += "[b]保存时间:[/b] %s\n" % save_info.save_time
	info_text += "[b]游戏版本:[/b] %s\n" % save_info.version
	info_text += "[b]文件大小:[/b] %s\n" % format_file_size(save_info.file_size)
	
	save_info_text.text = info_text

func format_file_size(size_bytes: int) -> String:
	"""格式化文件大小"""
	if size_bytes < 1024:
		return "%d B" % size_bytes
	elif size_bytes < 1024 * 1024:
		return "%.1f KB" % (size_bytes / 1024.0)
	else:
		return "%.1f MB" % (size_bytes / (1024.0 * 1024.0))

func _on_confirmed():
	"""确认加载"""
	if selected_save_index >= 0 and selected_save_index < save_data.size():
		var save_name = save_data[selected_save_index].name
		print("[LoadGameDialog] 加载存档: %s" % save_name)
		game_loaded.emit(save_name)
		hide()

func _on_canceled():
	"""取消加载"""
	hide()

func _on_delete_pressed():
	"""删除存档按钮点击"""
	if selected_save_index >= 0 and selected_save_index < save_data.size():
		show_delete_confirmation()

func _on_refresh_pressed():
	"""刷新按钮点击"""
	refresh_save_list()

func show_delete_confirmation():
	"""显示删除确认对话框"""
	var save_name = save_data[selected_save_index].name
	
	var confirmation = ConfirmationDialog.new()
	confirmation.title = "删除确认"
	confirmation.dialog_text = "确定要删除存档 '%s' 吗？\n此操作无法撤销！" % save_name
	add_child(confirmation)
	
	confirmation.confirmed.connect(_on_delete_confirmed.bind(save_name))
	confirmation.popup_centered()

func _on_delete_confirmed(save_name: String):
	"""确认删除存档"""
	print("[LoadGameDialog] 删除存档: %s" % save_name)
	
	var save_system = SaveSystem.new()
	if save_system.delete_save(save_name):
		show_message("删除成功", "存档 '%s' 已删除" % save_name)
		refresh_save_list()
	else:
		show_message("删除失败", "无法删除存档 '%s'" % save_name)

func show_message(title: String, message: String):
	"""显示消息对话框"""
	var msg_dialog = AcceptDialog.new()
	msg_dialog.title = title
	msg_dialog.dialog_text = message
	add_child(msg_dialog)
	msg_dialog.popup_centered()
	
	# 自动清理
	msg_dialog.confirmed.connect(func(): msg_dialog.queue_free())
