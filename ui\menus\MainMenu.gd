# MainMenu.gd
# 主菜单控制脚本
extends Control

# 节点引用
@onready var new_game_button = $MainContainer/MenuButtons/NewGameButton
@onready var load_game_button = $MainContainer/MenuButtons/LoadGameButton
@onready var settings_button = $MainContainer/MenuButtons/SettingsButton
@onready var quit_button = $MainContainer/MenuButtons/QuitButton
@onready var title_label = $MainContainer/Title
@onready var version_label = $MainContainer/VersionLabel

# 子菜单场景
var load_game_dialog: LoadGameDialog
var settings_dialog: SettingsDialog
var new_game_dialog: NewGameDialog

func _ready():
	"""初始化主菜单"""
	print("[MainMenu] 主菜单初始化")
	
	# 设置游戏状态
	if GameDataManager.instance:
		GameDataManager.instance.set_game_state(GameDataManager.GameState.MENU)
	
	# 初始化UI
	setup_ui()
	
	# 连接信号
	connect_signals()
	
	# 播放背景音乐
	play_background_music()

func setup_ui():
	"""设置UI"""
	# 设置标题样式
	if title_label:
		title_label.add_theme_font_size_override("font_size", 36)
	
	# 设置版本信息
	if version_label:
		version_label.text = "版本 " + ProjectSettings.get_setting("application/config/version", "1.0.0")
	
	# 设置按钮样式
	setup_button_styles()
	
	# 检查是否有存档
	update_load_button_state()

func setup_button_styles():
	"""设置按钮样式"""
	var buttons = [new_game_button, load_game_button, settings_button, quit_button]
	
	for button in buttons:
		if button:
			button.custom_minimum_size = Vector2(200, 50)
			button.add_theme_font_size_override("font_size", 18)

func connect_signals():
	"""连接信号"""
	# 按钮信号已在场景中连接
	
	# 连接场景管理器信号
	if SceneManager.instance:
		SceneManager.instance.scene_changed.connect(_on_scene_changed)

func update_load_button_state():
	"""更新加载游戏按钮状态"""
	if load_game_button:
		# 检查是否有存档文件
		var save_system = SaveSystem.new()
		var save_list = save_system.get_save_list()
		load_game_button.disabled = save_list.is_empty()
		
		if load_game_button.disabled:
			load_game_button.text = "加载游戏 (无存档)"
		else:
			load_game_button.text = "加载游戏"

func play_background_music():
	"""播放背景音乐"""
	# 这里可以添加背景音乐播放逻辑
	pass

# 按钮回调函数
func _on_new_game_pressed():
	"""新游戏按钮点击"""
	print("[MainMenu] 点击新游戏")
	show_new_game_dialog()

func _on_load_game_pressed():
	"""加载游戏按钮点击"""
	print("[MainMenu] 点击加载游戏")
	show_load_game_dialog()

func _on_settings_pressed():
	"""设置按钮点击"""
	print("[MainMenu] 点击设置")
	show_settings_dialog()

func _on_quit_pressed():
	"""退出游戏按钮点击"""
	print("[MainMenu] 点击退出游戏")
	show_quit_confirmation()

# 对话框显示方法
func show_new_game_dialog():
	"""显示新游戏对话框"""
	if not new_game_dialog:
		new_game_dialog = preload("res://ui/dialogs/NewGameDialog.tscn").instantiate()
		add_child(new_game_dialog)
		new_game_dialog.game_started.connect(_on_new_game_started)
	
	new_game_dialog.show_dialog()

func show_load_game_dialog():
	"""显示加载游戏对话框"""
	if not load_game_dialog:
		load_game_dialog = preload("res://ui/dialogs/LoadGameDialog.tscn").instantiate()
		add_child(load_game_dialog)
		load_game_dialog.game_loaded.connect(_on_game_loaded)
	
	load_game_dialog.show_dialog()

func show_settings_dialog():
	"""显示设置对话框"""
	if not settings_dialog:
		settings_dialog = preload("res://ui/dialogs/SettingsDialog.tscn").instantiate()
		add_child(settings_dialog)
	
	settings_dialog.show_dialog()

func show_quit_confirmation():
	"""显示退出确认对话框"""
	var confirmation = ConfirmationDialog.new()
	confirmation.dialog_text = "确定要退出游戏吗？"
	confirmation.title = "退出确认"
	add_child(confirmation)
	
	confirmation.confirmed.connect(_on_quit_confirmed)
	confirmation.popup_centered()

# 对话框回调
func _on_new_game_started(game_config: Dictionary):
	"""新游戏开始回调"""
	print("[MainMenu] 开始新游戏: %s" % game_config)
	
	# 初始化新游戏数据
	initialize_new_game(game_config)
	
	# 切换到游戏场景
	if SceneManager.instance:
		SceneManager.instance.start_game()

func _on_game_loaded(save_name: String):
	"""游戏加载回调"""
	print("[MainMenu] 加载游戏: %s" % save_name)
	
	# 加载游戏数据
	var save_system = SaveSystem.new()
	if save_system.load_game(save_name):
		# 切换到游戏场景
		if SceneManager.instance:
			SceneManager.instance.start_game()
	else:
		show_error_dialog("加载游戏失败", "无法加载存档文件: " + save_name)

func _on_quit_confirmed():
	"""退出确认回调"""
	print("[MainMenu] 确认退出游戏")
	if SceneManager.instance:
		SceneManager.instance.quit_game()

func _on_scene_changed(old_scene: String, new_scene: String):
	"""场景切换回调"""
	print("[MainMenu] 场景切换: %s -> %s" % [old_scene, new_scene])

# 游戏初始化
func initialize_new_game(game_config: Dictionary):
	"""初始化新游戏"""
	print("[MainMenu] 初始化新游戏数据...")
	
	if GameDataManager.instance:
		# 重置游戏数据
		GameDataManager.instance.game_data.clear()
		GameDataManager.instance.player_data.clear()
		
		# 设置玩家数据
		GameDataManager.instance.set_player_data("name", game_config.get("player_name", "玩家"))
		GameDataManager.instance.set_player_data("age", game_config.get("player_age", 25))
		GameDataManager.instance.set_player_data("profession", game_config.get("player_profession", "学生"))
		
		# 创建默认世界
		if GameDataManager.instance.world_data:
			GameDataManager.instance.world_data.create_default_world()
		
		# 重置时间系统
		if GameDataManager.instance.time_system:
			GameDataManager.instance.time_system.set_time(8, 1, 1, 1)  # 第1年1月1日8点
		
		print("[MainMenu] 新游戏数据初始化完成")

func show_error_dialog(title: String, message: String):
	"""显示错误对话框"""
	var error_dialog = AcceptDialog.new()
	error_dialog.title = title
	error_dialog.dialog_text = message
	add_child(error_dialog)
	error_dialog.popup_centered()
	
	# 自动清理
	error_dialog.confirmed.connect(func(): error_dialog.queue_free())

# 输入处理
func _input(event):
	"""处理输入事件"""
	if event.is_action_pressed("ui_cancel"):
		# ESC键退出游戏
		show_quit_confirmation()

func _exit_tree():
	"""清理资源"""
	print("[MainMenu] 主菜单清理完成")
