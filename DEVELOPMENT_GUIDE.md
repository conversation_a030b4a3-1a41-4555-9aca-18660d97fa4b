# LifeD - 沙盒模拟经营游戏开发文档

## 📖 项目概述

**项目名称**: LifeD - 生活模拟沙盒游戏  
**引擎**: Godot 4.4.1  
**开发语言**: GDScript  
**开发模式**: 单人AI辅助开发  
**项目类型**: 结合模拟经营、视觉小说的沙盒类游戏

## 🏗️ 技术架构

### 核心架构分层
```
┌─────────────────────────────────────┐
│           AI演出层 (Presentation)    │
│  ┌─────────┬─────────┬─────────────┐ │
│  │   LLM   │ 文生图  │    TTS      │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│          逻辑层 (Logic Layer)        │
│  ┌─────────┬─────────┬─────────────┐ │
│  │ 经济系统 │NPC行为  │  时间系统   │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│         数据层 (Data Layer)          │
│  ┌─────────┬─────────┬─────────────┐ │
│  │ 地图数据 │NPC数据  │  游戏状态   │ │
│  └─────────┴─────────┴─────────────┘ │
└─────────────────────────────────────┘
```

### 技术栈选择

| 组件 | 技术选择 | 理由 |
|------|----------|------|
| 游戏引擎 | Godot 4.4.1 | 开源、轻量、GDScript性能优秀 |
| NPC行为系统 | Beehave插件 | 成熟的行为树实现，模块化设计 |
| HTTP客户端 | Godot内置HTTPRequest | 异步支持，性能优秀 |
| UI框架 | Godot内置UI系统 | 响应式设计，易于扩展 |
| 数据序列化 | JSON + Godot Resource | 可读性好，调试方便 |

## 🎯 开发里程碑

### 第一阶段：核心架构搭建 (4-6周)
- [x] 技术调研完成
- [/] 项目初始化和基础框架
- [ ] 主菜单系统实现
- [ ] 核心管理器类创建
- [ ] 基础UI框架搭建

**交付物**: 可运行的项目框架，完整的主菜单系统

### 第二阶段：地图系统实现 (6-8周)
- [ ] 多层级地图数据结构
- [ ] 世界生成算法
- [ ] 行政区划管理系统
- [ ] 建筑空间管理
- [ ] 地图可视化和编辑工具

**交付物**: 完整的多层级地图系统，地图编辑器

### 第三阶段：NPC基础系统 (5-7周)
- [ ] NPC属性和需求系统
- [ ] Beehave行为树集成
- [ ] 基础AI决策逻辑
- [ ] NPC生成和管理器
- [ ] 行为调试工具

**交付物**: 功能完整的NPC行为系统

## 📊 性能指标目标

| 指标 | 目标值 | 优化策略 |
|------|--------|----------|
| 同时运行NPC数量 | 1000+ | LOD系统，行为树优化 |
| 帧率 | 60 FPS | 分帧处理，缓存优化 |
| 内存使用 | < 2GB | 对象池，资源管理 |
| AI响应时间 | < 2秒 | 请求队列，缓存机制 |
| 存档加载时间 | < 5秒 | 增量保存，压缩 |

## 🔧 关键技术实现

### 1. NPC行为系统架构
```gdscript
# NPC基础类结构
class_name NPC extends CharacterBody2D
- attributes: NPCAttributes (年龄、性格、技能等)
- needs: Dictionary (饥饿、精力、社交、金钱)
- behavior_tree: BeehaveTree (行为决策)
- schedule: DailySchedule (日程安排)
```

### 2. 经济系统模型
```gdscript
# 供需驱动的价格机制
price = base_price * (demand / max(supply, 1.0))
```

### 3. 时间系统设计
- 24小时回合制
- 精力值系统
- 事件调度器
- 时间加速功能

## 📁 项目文件结构
```
res://
├── core/                 # 核心系统
│   ├── managers/         # 管理器类
│   ├── data/            # 数据定义
│   └── utils/           # 工具类
├── systems/             # 各子系统
│   ├── npc/            # NPC系统
│   ├── economy/        # 经济系统
│   ├── map/            # 地图系统
│   └── time/           # 时间系统
├── ui/                  # 用户界面
│   ├── menus/          # 菜单界面
│   ├── hud/            # 游戏HUD
│   └── dialogs/        # 对话框
├── scenes/              # 场景文件
├── resources/           # 资源文件
│   ├── textures/       # 纹理
│   ├── audio/          # 音频
│   └── data/           # 数据文件
└── addons/              # 插件
    └── beehave/        # 行为树插件
```

## 🚀 优化策略

### 性能优化
1. **LOD系统**: 根据距离调整NPC模拟精度
2. **对象池**: 重用频繁创建的对象
3. **分帧处理**: 将昂贵操作分散到多帧
4. **缓存机制**: 缓存计算结果和查找结果

### 内存管理
1. **资源预加载**: 使用preload()优化加载
2. **垃圾回收**: 及时释放不用的对象
3. **纹理压缩**: 优化图像资源大小
4. **音频压缩**: 使用合适的音频格式

## 📋 开发规范

### 代码规范
- 使用snake_case命名变量和函数
- 使用PascalCase命名类和枚举
- 添加类型注解提高性能
- 编写清晰的注释和文档

### Git工作流
- main分支：稳定版本
- develop分支：开发版本
- feature分支：新功能开发
- 提交信息使用中文，格式：[类型] 描述

## 💻 关键代码示例

### 核心管理器实现
```gdscript
# GameDataManager.gd - 游戏数据管理器
class_name GameDataManager
extends Node

signal data_changed(key: String, value)
signal system_initialized(system_name: String)

static var instance: GameDataManager

var world_data: WorldData
var npc_manager: NPCManager
var economy_system: EconomySystem
var time_system: TimeSystem
var save_system: SaveSystem

func _ready():
    if instance == null:
        instance = self
        initialize_systems()
    else:
        queue_free()

func initialize_systems():
    world_data = WorldData.new()
    npc_manager = NPCManager.new()
    economy_system = EconomySystem.new()
    time_system = TimeSystem.new()
    save_system = SaveSystem.new()

    add_child(npc_manager)
    add_child(economy_system)
    add_child(time_system)
    add_child(save_system)

    print("游戏核心系统初始化完成")
```

### NPC行为树示例
```gdscript
# NPCBehavior.gd - NPC行为逻辑
class_name NPCBehavior
extends Node

@export var npc: NPC
@onready var behavior_tree = $BeehaveTree
@onready var blackboard = $BeehaveTree/Blackboard

func _ready():
    setup_blackboard()

func setup_blackboard():
    blackboard.set_value("npc_ref", npc)
    blackboard.set_value("needs", npc.needs)
    blackboard.set_value("location", npc.current_location)
```

### 经济系统核心
```gdscript
# EconomySystem.gd - 经济系统
class_name EconomySystem
extends Node

var markets: Dictionary = {}
var goods_database: Dictionary = {}
var price_history: Array[Dictionary] = []

func calculate_market_price(good_id: String, region_id: String) -> float:
    var market = markets.get(region_id, {})
    var supply = market.get("supply_" + good_id, 1.0)
    var demand = market.get("demand_" + good_id, 1.0)
    var base_price = goods_database[good_id].base_price

    return base_price * (demand / supply)
```

## 🎮 游戏设计文档

### 核心玩法循环
1. **角色扮演**: 玩家选择或创建角色
2. **日常生活**: 工作、社交、消费等活动
3. **经济参与**: 买卖商品、投资、创业
4. **社会互动**: 与NPC建立关系、参与事件
5. **政策影响**: 政府决策影响经济和社会

### NPC需求系统
```gdscript
# NPC基础需求
var needs = {
    "hunger": 100.0,      # 饥饿值 (0-100)
    "energy": 100.0,      # 精力值 (0-100)
    "social": 50.0,       # 社交需求 (0-100)
    "money": 0.0,         # 金钱
    "happiness": 50.0,    # 幸福度 (0-100)
    "health": 100.0       # 健康值 (0-100)
}
```

### 地图层级设计
```
世界 (World)
├── 国家 (Country) - 政治体制、法律制度
│   ├── 省份 (Province) - 地方政策、税收
│   │   ├── 城市 (City) - 城市规划、基础设施
│   │   │   ├── 区域 (District) - 功能分区
│   │   │   │   └── 建筑 (Building) - 具体场所
│   │   │   └── 村庄 (Village) - 农村地区
```

## 🔄 下一步计划

1. 完成第一阶段开发
2. 集成Beehave插件
3. 实现基础NPC行为
4. 开发地图系统原型
5. 集成AI服务接口

## 📚 参考资料

- [Godot 4.4 官方文档](https://docs.godotengine.org/)
- [Beehave 行为树插件](https://github.com/bitbrain/beehave)
- [GDScript 性能优化指南](https://docs.godotengine.org/en/stable/tutorials/performance/)
- [游戏架构设计模式](https://gameprogrammingpatterns.com/)

---

**文档版本**: v1.0
**最后更新**: 2025-01-16
**维护者**: AI开发助手
