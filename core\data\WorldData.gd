# WorldData.gd
# 世界数据类 - 存储游戏世界的所有数据
class_name WorldData
extends Resource

# 世界基础信息
@export var world_name: String = "新世界"
@export var creation_date: String = ""
@export var last_played: String = ""
@export var game_version: String = "1.0.0"

# 地图层级枚举
enum MapLevel {
	WORLD,      # 世界
	COUNTRY,    # 国家
	PROVINCE,   # 省份
	CITY,       # 市
	DISTRICT,   # 区
	VILLAGE     # 村
}

# 政体类型枚举
enum GovernmentType {
	REPUBLIC,       # 共和制
	MONARCHY,       # 君主制
	FEDERATION,     # 联邦制
	DEMOCRACY,      # 民主制
	AUTOCRACY       # 专制
}

# 世界设置
@export var world_settings: Dictionary = {
	"population_growth_rate": 0.02,
	"economic_volatility": 0.1,
	"disaster_frequency": 0.05,
	"technology_progress_rate": 0.01
}

# 地图数据
@export var map_nodes: Array[MapNode] = []
@export var countries: Array[Country] = []

# 全局统计数据
@export var global_stats: Dictionary = {
	"total_population": 0,
	"total_gdp": 0.0,
	"average_happiness": 50.0,
	"total_companies": 0
}

func _init():
	"""初始化世界数据"""
	creation_date = Time.get_datetime_string_from_system()
	last_played = creation_date

func create_default_world():
	"""创建默认世界"""
	print("[WorldData] 创建默认世界...")
	
	# 创建默认国家
	var default_country = Country.new()
	default_country.name = "华夏共和国"
	default_country.government_type = GovernmentType.REPUBLIC
	default_country.population = 1000000
	default_country.gdp = 50000000.0
	
	countries.append(default_country)
	
	# 创建地图节点
	create_default_map_structure(default_country)
	
	update_global_stats()
	print("[WorldData] 默认世界创建完成")

func create_default_map_structure(country: Country):
	"""创建默认地图结构"""
	# 创建世界根节点
	var world_node = MapNode.new()
	world_node.level = MapLevel.WORLD
	world_node.name = world_name
	map_nodes.append(world_node)
	
	# 创建国家节点
	var country_node = MapNode.new()
	country_node.level = MapLevel.COUNTRY
	country_node.name = country.name
	country_node.parent_id = world_node.id
	world_node.children.append(country_node)
	
	# 创建省份
	for i in range(3):
		var province_node = MapNode.new()
		province_node.level = MapLevel.PROVINCE
		province_node.name = "省份_%d" % (i + 1)
		province_node.parent_id = country_node.id
		country_node.children.append(province_node)
		
		# 创建城市
		for j in range(2):
			var city_node = MapNode.new()
			city_node.level = MapLevel.CITY
			city_node.name = "城市_%d_%d" % [i + 1, j + 1]
			city_node.parent_id = province_node.id
			province_node.children.append(city_node)
			
			# 创建区域
			for k in range(3):
				var district_node = MapNode.new()
				district_node.level = MapLevel.DISTRICT
				district_node.name = "区域_%d_%d_%d" % [i + 1, j + 1, k + 1]
				district_node.parent_id = city_node.id
				district_node.space_index = 100  # 建筑空间指数
				city_node.children.append(district_node)

func get_map_node_by_id(node_id: String) -> MapNode:
	"""根据ID获取地图节点"""
	return _find_node_recursive(map_nodes, node_id)

func _find_node_recursive(nodes: Array, target_id: String) -> MapNode:
	"""递归查找节点"""
	for node in nodes:
		if node.id == target_id:
			return node
		var found = _find_node_recursive(node.children, target_id)
		if found:
			return found
	return null

func get_nodes_by_level(level: MapLevel) -> Array[MapNode]:
	"""获取指定层级的所有节点"""
	var result: Array[MapNode] = []
	_collect_nodes_by_level(map_nodes, level, result)
	return result

func _collect_nodes_by_level(nodes: Array, target_level: MapLevel, result: Array):
	"""递归收集指定层级的节点"""
	for node in nodes:
		if node.level == target_level:
			result.append(node)
		_collect_nodes_by_level(node.children, target_level, result)

func update_global_stats():
	"""更新全局统计数据"""
	global_stats.total_population = 0
	global_stats.total_gdp = 0.0
	global_stats.total_companies = 0
	
	for country in countries:
		global_stats.total_population += country.population
		global_stats.total_gdp += country.gdp
	
	# 计算平均幸福度
	if countries.size() > 0:
		var total_happiness = 0.0
		for country in countries:
			total_happiness += country.happiness_index
		global_stats.average_happiness = total_happiness / countries.size()

func get_country_by_name(country_name: String) -> Country:
	"""根据名称获取国家"""
	for country in countries:
		if country.name == country_name:
			return country
	return null

func add_country(country: Country):
	"""添加国家"""
	countries.append(country)
	update_global_stats()

func remove_country(country_name: String) -> bool:
	"""移除国家"""
	for i in range(countries.size()):
		if countries[i].name == country_name:
			countries.remove_at(i)
			update_global_stats()
			return true
	return false

func save_to_file(file_path: String) -> bool:
	"""保存世界数据到文件"""
	last_played = Time.get_datetime_string_from_system()
	
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if file:
		var save_data = {
			"world_data": self,
			"save_version": "1.0.0",
			"save_time": last_played
		}
		file.store_string(JSON.stringify(save_data))
		file.close()
		print("[WorldData] 世界数据已保存: %s" % file_path)
		return true
	else:
		print("[WorldData] 保存失败: %s" % file_path)
		return false

func load_from_file(file_path: String) -> bool:
	"""从文件加载世界数据"""
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		if parse_result == OK:
			var save_data = json.data
			# 这里需要实现数据恢复逻辑
			print("[WorldData] 世界数据已加载: %s" % file_path)
			return true
		else:
			print("[WorldData] JSON解析失败: %s" % file_path)
			return false
	else:
		print("[WorldData] 文件读取失败: %s" % file_path)
		return false

func get_summary() -> Dictionary:
	"""获取世界数据摘要"""
	return {
		"world_name": world_name,
		"countries_count": countries.size(),
		"total_population": global_stats.total_population,
		"total_gdp": global_stats.total_gdp,
		"average_happiness": global_stats.average_happiness,
		"creation_date": creation_date,
		"last_played": last_played
	}
