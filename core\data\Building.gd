# Building.gd
# 建筑类 - 表示游戏中的建筑物
class_name Building
extends Resource

# 建筑类型枚举
enum BuildingType {
	RESIDENTIAL,    # 住宅
	COMMERCIAL,     # 商业
	INDUSTRIAL,     # 工业
	OFFICE,         # 办公
	EDUCATIONAL,    # 教育
	MEDICAL,        # 医疗
	ENTERTAINMENT,  # 娱乐
	GOVERNMENT,     # 政府
	RELIGIOUS,      # 宗教
	TRANSPORT       # 交通
}

# 基础信息
@export var id: String = ""
@export var name: String = ""
@export var building_type: BuildingType = BuildingType.RESIDENTIAL
@export var location_id: String = ""

# 空间信息
@export var space_cost: int = 10  # 占用的建筑空间指数
@export var floor_count: int = 1
@export var room_count: int = 1
@export var capacity: int = 10    # 容纳人数

# 经济信息
@export var construction_cost: float = 10000.0
@export var maintenance_cost: float = 100.0
@export var economic_value: float = 15000.0
@export var rent_price: float = 500.0

# 功能信息
@export var job_capacity: int = 0      # 提供的工作岗位
@export var services: Array[String] = [] # 提供的服务
@export var amenities: Array[String] = [] # 设施

# 状态信息
@export var condition: float = 100.0   # 建筑状况 (0-100)
@export var occupancy_rate: float = 0.0 # 入住率 (0-1)
@export var is_operational: bool = true

# 居住者/使用者
@export var occupants: Array[String] = []  # NPC IDs
@export var employees: Array[String] = []  # 员工 NPC IDs
@export var owner_id: String = ""          # 所有者ID

# 建筑特性
@export var energy_efficiency: float = 50.0  # 能源效率 (0-100)
@export var safety_rating: float = 80.0      # 安全等级 (0-100)
@export var accessibility: float = 70.0      # 无障碍程度 (0-100)

func _init():
	"""初始化建筑"""
	id = generate_unique_id()
	initialize_by_type()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "building_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func initialize_by_type():
	"""根据建筑类型初始化默认值"""
	match building_type:
		BuildingType.RESIDENTIAL:
			job_capacity = 0
			services = ["住宿"]
			amenities = ["厨房", "卫生间", "客厅"]
			capacity = 4
		BuildingType.COMMERCIAL:
			job_capacity = 5
			services = ["零售", "餐饮"]
			amenities = ["收银台", "储物间"]
			capacity = 20
		BuildingType.OFFICE:
			job_capacity = 10
			services = ["办公服务"]
			amenities = ["会议室", "休息室"]
			capacity = 15
		BuildingType.EDUCATIONAL:
			job_capacity = 3
			services = ["教育"]
			amenities = ["教室", "图书馆", "实验室"]
			capacity = 30
		BuildingType.MEDICAL:
			job_capacity = 8
			services = ["医疗", "急救"]
			amenities = ["诊室", "药房", "手术室"]
			capacity = 25
		BuildingType.ENTERTAINMENT:
			job_capacity = 6
			services = ["娱乐", "休闲"]
			amenities = ["舞台", "音响设备"]
			capacity = 50
		BuildingType.INDUSTRIAL:
			job_capacity = 20
			services = ["生产", "制造"]
			amenities = ["生产线", "仓库"]
			capacity = 30
		BuildingType.GOVERNMENT:
			job_capacity = 15
			services = ["政府服务", "行政"]
			amenities = ["办事大厅", "档案室"]
			capacity = 100
		BuildingType.RELIGIOUS:
			job_capacity = 2
			services = ["宗教服务"]
			amenities = ["祈祷室", "礼堂"]
			capacity = 100
		BuildingType.TRANSPORT:
			job_capacity = 5
			services = ["交通运输"]
			amenities = ["候车室", "售票处"]
			capacity = 200

func add_occupant(npc_id: String) -> bool:
	"""添加居住者"""
	if occupants.size() < capacity and npc_id not in occupants:
		occupants.append(npc_id)
		update_occupancy_rate()
		return true
	return false

func remove_occupant(npc_id: String) -> bool:
	"""移除居住者"""
	var index = occupants.find(npc_id)
	if index >= 0:
		occupants.remove_at(index)
		update_occupancy_rate()
		return true
	return false

func add_employee(npc_id: String) -> bool:
	"""添加员工"""
	if employees.size() < job_capacity and npc_id not in employees:
		employees.append(npc_id)
		return true
	return false

func remove_employee(npc_id: String) -> bool:
	"""移除员工"""
	var index = employees.find(npc_id)
	if index >= 0:
		employees.remove_at(index)
		return true
	return false

func update_occupancy_rate():
	"""更新入住率"""
	if capacity > 0:
		occupancy_rate = float(occupants.size()) / float(capacity)
	else:
		occupancy_rate = 0.0

func get_available_capacity() -> int:
	"""获取可用容量"""
	return capacity - occupants.size()

func get_available_jobs() -> int:
	"""获取可用工作岗位"""
	return job_capacity - employees.size()

func has_service(service_name: String) -> bool:
	"""检查是否提供指定服务"""
	return service_name in services

func has_amenity(amenity_name: String) -> bool:
	"""检查是否有指定设施"""
	return amenity_name in amenities

func calculate_monthly_income() -> float:
	"""计算月收入"""
	var income = 0.0
	
	match building_type:
		BuildingType.RESIDENTIAL:
			income = rent_price * occupants.size()
		BuildingType.COMMERCIAL:
			income = economic_value * 0.1 * occupancy_rate
		BuildingType.OFFICE:
			income = rent_price * employees.size()
		_:
			income = economic_value * 0.05
	
	return income

func calculate_monthly_expenses() -> float:
	"""计算月支出"""
	var expenses = maintenance_cost
	
	# 根据建筑状况调整维护费用
	if condition < 50.0:
		expenses *= 1.5
	elif condition < 80.0:
		expenses *= 1.2
	
	# 员工工资
	expenses += employees.size() * 2000.0
	
	return expenses

func update_condition(delta_time: float):
	"""更新建筑状况"""
	# 自然老化
	var aging_rate = 0.1 / (365.0 * 24.0 * 3600.0)  # 每年老化0.1%
	condition = max(0.0, condition - aging_rate * delta_time)
	
	# 使用磨损
	var usage_factor = occupancy_rate * 0.5
	var wear_rate = usage_factor * 0.05 / (365.0 * 24.0 * 3600.0)
	condition = max(0.0, condition - wear_rate * delta_time)

func repair_building(repair_amount: float) -> float:
	"""维修建筑"""
	var old_condition = condition
	condition = min(100.0, condition + repair_amount)
	var repair_cost = (condition - old_condition) * construction_cost * 0.01
	return repair_cost

func get_building_type_name() -> String:
	"""获取建筑类型名称"""
	match building_type:
		BuildingType.RESIDENTIAL:
			return "住宅"
		BuildingType.COMMERCIAL:
			return "商业"
		BuildingType.INDUSTRIAL:
			return "工业"
		BuildingType.OFFICE:
			return "办公"
		BuildingType.EDUCATIONAL:
			return "教育"
		BuildingType.MEDICAL:
			return "医疗"
		BuildingType.ENTERTAINMENT:
			return "娱乐"
		BuildingType.GOVERNMENT:
			return "政府"
		BuildingType.RELIGIOUS:
			return "宗教"
		BuildingType.TRANSPORT:
			return "交通"
		_:
			return "未知"

func get_building_info() -> Dictionary:
	"""获取建筑信息摘要"""
	return {
		"id": id,
		"name": name,
		"type": get_building_type_name(),
		"occupancy_rate": occupancy_rate,
		"condition": condition,
		"monthly_income": calculate_monthly_income(),
		"monthly_expenses": calculate_monthly_expenses(),
		"available_capacity": get_available_capacity(),
		"available_jobs": get_available_jobs()
	}

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"building_type": building_type,
		"location_id": location_id,
		"space_cost": space_cost,
		"capacity": capacity,
		"condition": condition,
		"occupancy_rate": occupancy_rate,
		"occupants": occupants.duplicate(),
		"employees": employees.duplicate(),
		"services": services.duplicate(),
		"amenities": amenities.duplicate()
	}
