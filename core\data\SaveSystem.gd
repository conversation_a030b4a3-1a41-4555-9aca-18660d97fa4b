# SaveSystem.gd
# 存档系统 - 管理游戏存档的保存和加载
class_name SaveSystem
extends Node

# 信号定义
signal save_started(save_name: String)
signal save_completed(save_name: String, success: bool)
signal load_started(save_name: String)
signal load_completed(save_name: String, success: bool)

# 存档路径
const SAVE_DIR = "user://saves/"
const SAVE_EXTENSION = ".lifed"
const SETTINGS_FILE = "user://settings.cfg"

# 存档数据
var current_save_name: String = ""
var auto_save_enabled: bool = true
var auto_save_interval: float = 300.0  # 5分钟自动保存
var auto_save_timer: float = 0.0

func _ready():
	"""初始化存档系统"""
	print("[SaveSystem] 存档系统初始化")
	ensure_save_directory()
	load_settings()

func _process(delta):
	"""处理自动保存"""
	if auto_save_enabled and current_save_name != "":
		auto_save_timer += delta
		if auto_save_timer >= auto_save_interval:
			auto_save_timer = 0.0
			auto_save()

func ensure_save_directory():
	"""确保存档目录存在"""
	if not DirAccess.dir_exists_absolute(SAVE_DIR):
		DirAccess.open("user://").make_dir_recursive("saves")
		print("[SaveSystem] 创建存档目录: %s" % SAVE_DIR)

func save_game(save_name: String) -> bool:
	"""保存游戏"""
	save_started.emit(save_name)
	print("[SaveSystem] 开始保存游戏: %s" % save_name)
	
	var save_data = collect_save_data()
	var file_path = SAVE_DIR + save_name + SAVE_EXTENSION
	
	var success = write_save_file(file_path, save_data)
	
	if success:
		current_save_name = save_name
		print("[SaveSystem] 游戏保存成功: %s" % file_path)
	else:
		print("[SaveSystem] 游戏保存失败: %s" % file_path)
	
	save_completed.emit(save_name, success)
	return success

func load_game(save_name: String) -> bool:
	"""加载游戏"""
	load_started.emit(save_name)
	print("[SaveSystem] 开始加载游戏: %s" % save_name)
	
	var file_path = SAVE_DIR + save_name + SAVE_EXTENSION
	var save_data = read_save_file(file_path)
	
	var success = false
	if save_data != null:
		success = apply_save_data(save_data)
		if success:
			current_save_name = save_name
			print("[SaveSystem] 游戏加载成功: %s" % file_path)
		else:
			print("[SaveSystem] 游戏数据应用失败: %s" % file_path)
	else:
		print("[SaveSystem] 游戏加载失败: %s" % file_path)
	
	load_completed.emit(save_name, success)
	return success

func collect_save_data() -> Dictionary:
	"""收集存档数据"""
	var save_data = {
		"version": "1.0.0",
		"save_time": Time.get_datetime_string_from_system(),
		"game_data": {},
		"world_data": {},
		"player_data": {},
		"time_data": {},
		"npc_data": {},
		"economy_data": {}
	}
	
	# 收集游戏数据管理器的数据
	if GameDataManager.instance:
		save_data.game_data = GameDataManager.instance.game_data.duplicate()
		save_data.player_data = GameDataManager.instance.player_data.duplicate()
		
		# 收集世界数据
		if GameDataManager.instance.world_data:
			save_data.world_data = GameDataManager.instance.world_data.to_dict()
		
		# 收集时间数据
		if GameDataManager.instance.time_system:
			save_data.time_data = GameDataManager.instance.time_system.get_time_data()
		
		# 收集NPC数据
		if GameDataManager.instance.npc_manager:
			save_data.npc_data = collect_npc_data()
		
		# 收集经济数据
		if GameDataManager.instance.economy_system:
			save_data.economy_data = collect_economy_data()
	
	return save_data

func collect_npc_data() -> Dictionary:
	"""收集NPC数据"""
	var npc_data = {
		"npcs": {},
		"statistics": {}
	}
	
	var npc_manager = GameDataManager.instance.npc_manager
	if npc_manager:
		# 收集所有NPC的数据
		for npc_id in npc_manager.npcs.keys():
			var npc = npc_manager.npcs[npc_id]
			npc_data.npcs[npc_id] = npc.to_dict()
		
		# 收集统计数据
		npc_data.statistics = npc_manager.get_npc_statistics()
	
	return npc_data

func collect_economy_data() -> Dictionary:
	"""收集经济数据"""
	var economy_data = {
		"markets": {},
		"goods_database": {},
		"price_history": {}
	}
	
	var economy_system = GameDataManager.instance.economy_system
	if economy_system:
		economy_data.markets = economy_system.markets.duplicate()
		economy_data.goods_database = economy_system.goods_database.duplicate()
		economy_data.price_history = economy_system.price_history.duplicate()
	
	return economy_data

func apply_save_data(save_data: Dictionary) -> bool:
	"""应用存档数据"""
	try:
		# 应用游戏数据
		if save_data.has("game_data"):
			GameDataManager.instance.game_data = save_data.game_data.duplicate()
		
		if save_data.has("player_data"):
			GameDataManager.instance.player_data = save_data.player_data.duplicate()
		
		# 应用时间数据
		if save_data.has("time_data") and GameDataManager.instance.time_system:
			apply_time_data(save_data.time_data)
		
		# 应用世界数据
		if save_data.has("world_data"):
			apply_world_data(save_data.world_data)
		
		# 应用NPC数据
		if save_data.has("npc_data") and GameDataManager.instance.npc_manager:
			apply_npc_data(save_data.npc_data)
		
		# 应用经济数据
		if save_data.has("economy_data") and GameDataManager.instance.economy_system:
			apply_economy_data(save_data.economy_data)
		
		return true
	except:
		print("[SaveSystem] 应用存档数据时发生错误")
		return false

func apply_time_data(time_data: Dictionary):
	"""应用时间数据"""
	var time_system = GameDataManager.instance.time_system
	if time_data.has("hour") and time_data.has("day") and time_data.has("month") and time_data.has("year"):
		time_system.set_time(time_data.hour, time_data.day, time_data.month, time_data.year)
	
	if time_data.has("time_scale"):
		time_system.set_time_scale(time_data.time_scale)

func apply_world_data(world_data: Dictionary):
	"""应用世界数据"""
	# 这里需要实现世界数据的恢复逻辑
	pass

func apply_npc_data(npc_data: Dictionary):
	"""应用NPC数据"""
	# 这里需要实现NPC数据的恢复逻辑
	pass

func apply_economy_data(economy_data: Dictionary):
	"""应用经济数据"""
	var economy_system = GameDataManager.instance.economy_system
	if economy_data.has("markets"):
		economy_system.markets = economy_data.markets.duplicate()
	
	if economy_data.has("price_history"):
		economy_system.price_history = economy_data.price_history.duplicate()

func write_save_file(file_path: String, save_data: Dictionary) -> bool:
	"""写入存档文件"""
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(save_data)
		file.store_string(json_string)
		file.close()
		return true
	return false

func read_save_file(file_path: String) -> Dictionary:
	"""读取存档文件"""
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		if parse_result == OK:
			return json.data
	
	return {}

func get_save_list() -> Array[Dictionary]:
	"""获取存档列表"""
	var saves: Array[Dictionary] = []
	var dir = DirAccess.open(SAVE_DIR)
	
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if file_name.ends_with(SAVE_EXTENSION):
				var save_name = file_name.get_basename()
				var file_path = SAVE_DIR + file_name
				var save_info = get_save_info(file_path)
				save_info["name"] = save_name
				saves.append(save_info)
			
			file_name = dir.get_next()
	
	return saves

func get_save_info(file_path: String) -> Dictionary:
	"""获取存档信息"""
	var save_data = read_save_file(file_path)
	if save_data.has("save_time"):
		return {
			"save_time": save_data.save_time,
			"version": save_data.get("version", "未知"),
			"file_size": FileAccess.get_file_as_bytes(file_path).size()
		}
	
	return {
		"save_time": "未知",
		"version": "未知",
		"file_size": 0
	}

func auto_save():
	"""自动保存"""
	if current_save_name != "":
		var auto_save_name = current_save_name + "_auto"
		save_game(auto_save_name)
		print("[SaveSystem] 自动保存完成: %s" % auto_save_name)

func delete_save(save_name: String) -> bool:
	"""删除存档"""
	var file_path = SAVE_DIR + save_name + SAVE_EXTENSION
	if FileAccess.file_exists(file_path):
		DirAccess.open(SAVE_DIR).remove(save_name + SAVE_EXTENSION)
		print("[SaveSystem] 删除存档: %s" % save_name)
		return true
	return false

func save_settings():
	"""保存设置"""
	var config = ConfigFile.new()
	config.set_value("save", "auto_save_enabled", auto_save_enabled)
	config.set_value("save", "auto_save_interval", auto_save_interval)
	config.save(SETTINGS_FILE)

func load_settings():
	"""加载设置"""
	var config = ConfigFile.new()
	if config.load(SETTINGS_FILE) == OK:
		auto_save_enabled = config.get_value("save", "auto_save_enabled", true)
		auto_save_interval = config.get_value("save", "auto_save_interval", 300.0)

func cleanup():
	"""清理存档系统"""
	save_settings()
	print("[SaveSystem] 存档系统清理完成")
