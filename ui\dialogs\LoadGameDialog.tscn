[gd_scene load_steps=2 format=3 uid="uid://bqhqhqhqhqhqk"]

[ext_resource type="Script" path="res://ui/dialogs/LoadGameDialog.gd" id="1_1a2b5"]

[node name="LoadGameDialog" type="AcceptDialog"]
title = "加载游戏"
initial_position = 2
size = Vector2i(600, 500)
script = ExtResource("1_1a2b5")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.0
offset_top = 8.0
offset_right = -8.0
offset_bottom = -49.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "选择存档"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="SaveListContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="SaveListLabel" type="Label" parent="VBoxContainer/SaveListContainer"]
layout_mode = 2
text = "存档列表:"

[node name="SaveList" type="ItemList" parent="VBoxContainer/SaveListContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="SaveInfoContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="SaveInfoLabel" type="Label" parent="VBoxContainer/SaveInfoContainer"]
layout_mode = 2
text = "存档信息:"

[node name="SaveInfoText" type="RichTextLabel" parent="VBoxContainer/SaveInfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 100)
bbcode_enabled = true
text = "请选择一个存档查看详细信息"

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="DeleteButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "删除存档"
disabled = true

[node name="Spacer" type="Control" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RefreshButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "刷新列表"

[connection signal="item_selected" from="VBoxContainer/SaveListContainer/SaveList" to="." method="_on_save_selected"]
[connection signal="pressed" from="VBoxContainer/ButtonContainer/DeleteButton" to="." method="_on_delete_pressed"]
[connection signal="pressed" from="VBoxContainer/ButtonContainer/RefreshButton" to="." method="_on_refresh_pressed"]
