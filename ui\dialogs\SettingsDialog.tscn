[gd_scene load_steps=2 format=3 uid="uid://bqhqhqhqhqhql"]

[ext_resource type="Script" path="res://ui/dialogs/SettingsDialog.gd" id="1_1a2b6"]

[node name="SettingsDialog" type="AcceptDialog"]
title = "游戏设置"
initial_position = 2
size = Vector2i(500, 600)
script = ExtResource("1_1a2b6")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.0
offset_top = 8.0
offset_right = -8.0
offset_bottom = -49.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "游戏设置"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="TabContainer" type="TabContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="音频" type="Control" parent="VBoxContainer/TabContainer"]
layout_mode = 2

[node name="AudioContainer" type="VBoxContainer" parent="VBoxContainer/TabContainer/音频"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MasterVolumeContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/音频/AudioContainer"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "主音量:"

[node name="MasterVolumeSlider" type="HSlider" parent="VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 1.0

[node name="MasterVolumeValue" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
text = "100%"

[node name="MusicVolumeContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/音频/AudioContainer"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "音乐音量:"

[node name="MusicVolumeSlider" type="HSlider" parent="VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.8

[node name="MusicVolumeValue" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
text = "80%"

[node name="SFXVolumeContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/音频/AudioContainer"]
layout_mode = 2

[node name="SFXVolumeLabel" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "音效音量:"

[node name="SFXVolumeSlider" type="HSlider" parent="VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 1.0

[node name="SFXVolumeValue" type="Label" parent="VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
text = "100%"

[node name="显示" type="Control" parent="VBoxContainer/TabContainer"]
layout_mode = 2
visible = false

[node name="DisplayContainer" type="VBoxContainer" parent="VBoxContainer/TabContainer/显示"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="FullscreenContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/显示/DisplayContainer"]
layout_mode = 2

[node name="FullscreenLabel" type="Label" parent="VBoxContainer/TabContainer/显示/DisplayContainer/FullscreenContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "全屏模式:"

[node name="FullscreenCheckBox" type="CheckBox" parent="VBoxContainer/TabContainer/显示/DisplayContainer/FullscreenContainer"]
layout_mode = 2

[node name="VSyncContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/显示/DisplayContainer"]
layout_mode = 2

[node name="VSyncLabel" type="Label" parent="VBoxContainer/TabContainer/显示/DisplayContainer/VSyncContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "垂直同步:"

[node name="VSyncCheckBox" type="CheckBox" parent="VBoxContainer/TabContainer/显示/DisplayContainer/VSyncContainer"]
layout_mode = 2
button_pressed = true

[node name="游戏" type="Control" parent="VBoxContainer/TabContainer"]
layout_mode = 2
visible = false

[node name="GameContainer" type="VBoxContainer" parent="VBoxContainer/TabContainer/游戏"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="AutoSaveContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/游戏/GameContainer"]
layout_mode = 2

[node name="AutoSaveLabel" type="Label" parent="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "自动保存:"

[node name="AutoSaveCheckBox" type="CheckBox" parent="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveContainer"]
layout_mode = 2
button_pressed = true

[node name="AutoSaveIntervalContainer" type="HBoxContainer" parent="VBoxContainer/TabContainer/游戏/GameContainer"]
layout_mode = 2

[node name="AutoSaveIntervalLabel" type="Label" parent="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveIntervalContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "保存间隔:"

[node name="AutoSaveIntervalSpinBox" type="SpinBox" parent="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveIntervalContainer"]
layout_mode = 2
min_value = 1.0
max_value = 30.0
value = 5.0
suffix = "分钟"

[connection signal="value_changed" from="VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer/MasterVolumeSlider" to="." method="_on_master_volume_changed"]
[connection signal="value_changed" from="VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer/MusicVolumeSlider" to="." method="_on_music_volume_changed"]
[connection signal="value_changed" from="VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer/SFXVolumeSlider" to="." method="_on_sfx_volume_changed"]
[connection signal="toggled" from="VBoxContainer/TabContainer/显示/DisplayContainer/FullscreenContainer/FullscreenCheckBox" to="." method="_on_fullscreen_toggled"]
[connection signal="toggled" from="VBoxContainer/TabContainer/显示/DisplayContainer/VSyncContainer/VSyncCheckBox" to="." method="_on_vsync_toggled"]
[connection signal="toggled" from="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveContainer/AutoSaveCheckBox" to="." method="_on_auto_save_toggled"]
[connection signal="value_changed" from="VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveIntervalContainer/AutoSaveIntervalSpinBox" to="." method="_on_auto_save_interval_changed"]
