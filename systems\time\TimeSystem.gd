# TimeSystem.gd
# 时间系统 - 管理游戏时间和回合制逻辑
class_name TimeSystem
extends Node

# 信号定义
signal hour_passed(hour: int)
signal day_passed(day: int)
signal week_passed(week: int)
signal month_passed(month: int)
signal turn_processed()
signal time_scale_changed(new_scale: float)

# 时间参数
@export var hours_per_day: int = 24
@export var days_per_week: int = 7
@export var days_per_month: int = 30
@export var time_scale: float = 1.0  # 时间加速倍率

# 当前时间
var current_hour: int = 8  # 从早上8点开始
var current_day: int = 1
var current_week: int = 1
var current_month: int = 1
var current_year: int = 1

# 控制状态
var is_paused: bool = false
var auto_advance: bool = true

# 内部计时器
var time_accumulator: float = 0.0
var base_time_per_hour: float = 1.0  # 每小时的基础时间（秒）

func _ready():
	"""初始化时间系统"""
	print("[TimeSystem] 时间系统初始化")
	print("[TimeSystem] 当前时间: %d年%d月%d周%d日 %d:00" % [current_year, current_month, current_week, current_day, current_hour])

func _process(delta):
	"""每帧更新时间"""
	if not is_paused and auto_advance:
		time_accumulator += delta * time_scale
		
		if time_accumulator >= base_time_per_hour:
			time_accumulator -= base_time_per_hour
			advance_hour()

func advance_hour():
	"""推进一小时"""
	current_hour += 1
	
	if current_hour >= hours_per_day:
		current_hour = 0
		advance_day()
	
	hour_passed.emit(current_hour)
	process_hourly_events()

func advance_day():
	"""推进一天"""
	current_day += 1
	
	# 检查是否需要推进周
	if (current_day - 1) % days_per_week == 0 and current_day > 1:
		advance_week()
	
	# 检查是否需要推进月
	if current_day > days_per_month:
		current_day = 1
		advance_month()
	
	day_passed.emit(current_day)
	process_daily_events()

func advance_week():
	"""推进一周"""
	current_week += 1
	week_passed.emit(current_week)
	process_weekly_events()

func advance_month():
	"""推进一月"""
	current_month += 1
	
	if current_month > 12:
		current_month = 1
		current_year += 1
	
	month_passed.emit(current_month)
	process_monthly_events()

func process_hourly_events():
	"""处理每小时事件"""
	# 这里可以添加每小时需要处理的逻辑
	pass

func process_daily_events():
	"""处理每日事件"""
	# 处理NPC日常行为重置
	var npc_manager = GameDataManager.get_npc_manager()
	if npc_manager:
		# 重置NPC的日常状态
		pass

func process_weekly_events():
	"""处理每周事件"""
	# 处理周期性事件
	pass

func process_monthly_events():
	"""处理每月事件"""
	# 处理经济更新、政策效果等
	var economy_system = GameDataManager.get_economy_system()
	if economy_system:
		economy_system.update_markets()

func set_time_scale(new_scale: float):
	"""设置时间倍率"""
	time_scale = max(0.1, min(10.0, new_scale))  # 限制在0.1x到10x之间
	time_scale_changed.emit(time_scale)
	print("[TimeSystem] 时间倍率设置为: %.1fx" % time_scale)

func pause_time():
	"""暂停时间"""
	is_paused = true
	print("[TimeSystem] 时间已暂停")

func resume_time():
	"""恢复时间"""
	is_paused = false
	print("[TimeSystem] 时间已恢复")

func toggle_pause():
	"""切换暂停状态"""
	if is_paused:
		resume_time()
	else:
		pause_time()

func set_time(hour: int, day: int, month: int, year: int):
	"""设置时间"""
	current_hour = clamp(hour, 0, hours_per_day - 1)
	current_day = clamp(day, 1, days_per_month)
	current_month = clamp(month, 1, 12)
	current_year = max(1, year)
	
	# 重新计算周数
	current_week = ((current_day - 1) / days_per_week) + 1
	
	print("[TimeSystem] 时间设置为: %d年%d月%d周%d日 %d:00" % [current_year, current_month, current_week, current_day, current_hour])

func get_current_time_string() -> String:
	"""获取当前时间字符串"""
	return "%d年%d月%d周%d日 %d:00" % [current_year, current_month, current_week, current_day, current_hour]

func get_time_of_day() -> String:
	"""获取一天中的时间段"""
	if current_hour >= 6 and current_hour < 12:
		return "上午"
	elif current_hour >= 12 and current_hour < 18:
		return "下午"
	elif current_hour >= 18 and current_hour < 22:
		return "晚上"
	else:
		return "深夜"

func is_working_hours() -> bool:
	"""检查是否为工作时间"""
	return current_hour >= 9 and current_hour < 17

func is_weekend() -> bool:
	"""检查是否为周末"""
	var day_of_week = (current_day - 1) % days_per_week
	return day_of_week >= 5  # 周六和周日

func get_time_data() -> Dictionary:
	"""获取时间数据"""
	return {
		"hour": current_hour,
		"day": current_day,
		"week": current_week,
		"month": current_month,
		"year": current_year,
		"time_scale": time_scale,
		"is_paused": is_paused,
		"time_string": get_current_time_string(),
		"time_of_day": get_time_of_day(),
		"is_working_hours": is_working_hours(),
		"is_weekend": is_weekend()
	}

func cleanup():
	"""清理时间系统"""
	is_paused = true
	time_accumulator = 0.0
	print("[TimeSystem] 时间系统清理完成")
