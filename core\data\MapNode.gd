# MapNode.gd
# 地图节点类 - 表示地图层级结构中的一个节点
class_name MapNode
extends Resource

# 节点基础信息
@export var id: String = ""
@export var name: String = ""
@export var level: WorldData.MapLevel
@export var parent_id: String = ""

# 层级关系
@export var children: Array[MapNode] = []

# 地理信息
@export var position: Vector2 = Vector2.ZERO
@export var size: Vector2 = Vector2(100, 100)
@export var climate_type: ClimateType = ClimateType.TEMPERATE

# 建筑和空间管理
@export var buildings: Array[Building] = []
@export var space_index: int = 100  # 建筑空间指数上限
@export var current_space_used: int = 0

# 人口和经济数据
@export var population: int = 0
@export var gdp_per_capita: float = 0.0
@export var unemployment_rate: float = 0.05
@export var happiness_index: float = 50.0

# 资源数据
@export var natural_resources: Dictionary = {}
@export var infrastructure_level: float = 50.0

# 政策和法规
@export var local_policies: Array[Policy] = []
@export var tax_rate: float = 0.15

# 气候类型枚举
enum ClimateType {
	TROPICAL,       # 热带
	TEMPERATE,      # 温带
	ARCTIC,         # 寒带
	DESERT,         # 沙漠
	MEDITERRANEAN   # 地中海
}

func _init():
	"""初始化地图节点"""
	id = generate_unique_id()
	initialize_default_values()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "node_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func initialize_default_values():
	"""初始化默认值"""
	match level:
		WorldData.MapLevel.WORLD:
			space_index = 0  # 世界级别不需要建筑空间
		WorldData.MapLevel.COUNTRY:
			space_index = 0  # 国家级别不需要建筑空间
		WorldData.MapLevel.PROVINCE:
			space_index = 0  # 省份级别不需要建筑空间
		WorldData.MapLevel.CITY:
			space_index = 500  # 城市有较大的建筑空间
		WorldData.MapLevel.DISTRICT:
			space_index = 100  # 区域有基础建筑空间
		WorldData.MapLevel.VILLAGE:
			space_index = 50   # 村庄建筑空间较小

func add_child_node(child: MapNode):
	"""添加子节点"""
	if child and child.id != id:
		child.parent_id = id
		children.append(child)

func remove_child_node(child_id: String) -> bool:
	"""移除子节点"""
	for i in range(children.size()):
		if children[i].id == child_id:
			children.remove_at(i)
			return true
	return false

func get_child_by_id(child_id: String) -> MapNode:
	"""根据ID获取子节点"""
	for child in children:
		if child.id == child_id:
			return child
	return null

func get_children_by_level(target_level: WorldData.MapLevel) -> Array[MapNode]:
	"""获取指定层级的子节点"""
	var result: Array[MapNode] = []
	for child in children:
		if child.level == target_level:
			result.append(child)
	return result

func add_building(building: Building) -> bool:
	"""添加建筑"""
	if can_add_building(building):
		buildings.append(building)
		current_space_used += building.space_cost
		building.location_id = id
		return true
	return false

func remove_building(building_id: String) -> bool:
	"""移除建筑"""
	for i in range(buildings.size()):
		if buildings[i].id == building_id:
			current_space_used -= buildings[i].space_cost
			buildings.remove_at(i)
			return true
	return false

func can_add_building(building: Building) -> bool:
	"""检查是否可以添加建筑"""
	return current_space_used + building.space_cost <= space_index

func get_available_space() -> int:
	"""获取可用空间"""
	return space_index - current_space_used

func get_space_usage_percentage() -> float:
	"""获取空间使用率"""
	if space_index <= 0:
		return 0.0
	return float(current_space_used) / float(space_index) * 100.0

func add_policy(policy: Policy):
	"""添加政策"""
	local_policies.append(policy)
	apply_policy_effects(policy)

func remove_policy(policy_id: String) -> bool:
	"""移除政策"""
	for i in range(local_policies.size()):
		if local_policies[i].id == policy_id:
			remove_policy_effects(local_policies[i])
			local_policies.remove_at(i)
			return true
	return false

func apply_policy_effects(policy: Policy):
	"""应用政策效果"""
	# 这里实现政策对节点的影响
	match policy.type:
		Policy.PolicyType.TAX:
			tax_rate += policy.effect_value
		Policy.PolicyType.INFRASTRUCTURE:
			infrastructure_level += policy.effect_value
		Policy.PolicyType.WELFARE:
			happiness_index += policy.effect_value

func remove_policy_effects(policy: Policy):
	"""移除政策效果"""
	match policy.type:
		Policy.PolicyType.TAX:
			tax_rate -= policy.effect_value
		Policy.PolicyType.INFRASTRUCTURE:
			infrastructure_level -= policy.effect_value
		Policy.PolicyType.WELFARE:
			happiness_index -= policy.effect_value

func update_economic_data():
	"""更新经济数据"""
	# 计算GDP
	var total_building_value = 0.0
	for building in buildings:
		total_building_value += building.economic_value
	
	if population > 0:
		gdp_per_capita = total_building_value / population
	
	# 更新失业率（基于建筑提供的工作岗位）
	var total_jobs = 0
	for building in buildings:
		total_jobs += building.job_capacity
	
	if population > 0:
		var working_population = population * 0.6  # 假设60%的人口为劳动人口
		unemployment_rate = max(0.0, (working_population - total_jobs) / working_population)

func get_total_population() -> int:
	"""获取总人口（包括子节点）"""
	var total = population
	for child in children:
		total += child.get_total_population()
	return total

func get_total_gdp() -> float:
	"""获取总GDP（包括子节点）"""
	var total = gdp_per_capita * population
	for child in children:
		total += child.get_total_gdp()
	return total

func get_average_happiness() -> float:
	"""获取平均幸福度（包括子节点）"""
	var total_happiness = happiness_index * population
	var total_pop = population
	
	for child in children:
		var child_pop = child.get_total_population()
		total_happiness += child.get_average_happiness() * child_pop
		total_pop += child_pop
	
	if total_pop > 0:
		return total_happiness / total_pop
	return 0.0

func get_node_info() -> Dictionary:
	"""获取节点信息摘要"""
	return {
		"id": id,
		"name": name,
		"level": WorldData.MapLevel.keys()[level],
		"population": population,
		"gdp_per_capita": gdp_per_capita,
		"happiness_index": happiness_index,
		"unemployment_rate": unemployment_rate,
		"space_usage": get_space_usage_percentage(),
		"children_count": children.size(),
		"buildings_count": buildings.size()
	}

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"level": level,
		"parent_id": parent_id,
		"position": {"x": position.x, "y": position.y},
		"size": {"x": size.x, "y": size.y},
		"population": population,
		"gdp_per_capita": gdp_per_capita,
		"happiness_index": happiness_index,
		"space_index": space_index,
		"current_space_used": current_space_used,
		"children_ids": children.map(func(child): return child.id)
	}
