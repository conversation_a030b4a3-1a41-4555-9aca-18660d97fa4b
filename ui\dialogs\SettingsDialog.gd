# SettingsDialog.gd
# 设置对话框
extends AcceptDialog

# 节点引用 - 音频
@onready var master_volume_slider = $VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer/MasterVolumeSlider
@onready var master_volume_value = $VBoxContainer/TabContainer/音频/AudioContainer/MasterVolumeContainer/MasterVolumeValue
@onready var music_volume_slider = $VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer/MusicVolumeSlider
@onready var music_volume_value = $VBoxContainer/TabContainer/音频/AudioContainer/MusicVolumeContainer/MusicVolumeValue
@onready var sfx_volume_slider = $VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer/SFXVolumeSlider
@onready var sfx_volume_value = $VBoxContainer/TabContainer/音频/AudioContainer/SFXVolumeContainer/SFXVolumeValue

# 节点引用 - 显示
@onready var fullscreen_checkbox = $VBoxContainer/TabContainer/显示/DisplayContainer/FullscreenContainer/FullscreenCheckBox
@onready var vsync_checkbox = $VBoxContainer/TabContainer/显示/DisplayContainer/VSyncContainer/VSyncCheckBox

# 节点引用 - 游戏
@onready var auto_save_checkbox = $VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveContainer/AutoSaveCheckBox
@onready var auto_save_interval_spinbox = $VBoxContainer/TabContainer/游戏/GameContainer/AutoSaveIntervalContainer/AutoSaveIntervalSpinBox

func _ready():
	"""初始化设置对话框"""
	connect_signals()
	load_current_settings()

func connect_signals():
	"""连接信号"""
	confirmed.connect(_on_confirmed)
	canceled.connect(_on_canceled)

func show_dialog():
	"""显示对话框"""
	load_current_settings()
	popup_centered()

func load_current_settings():
	"""加载当前设置"""
	if not GameDataManager.instance:
		return
	
	# 加载音频设置
	master_volume_slider.value = GameDataManager.instance.get_setting("master_volume", 1.0)
	music_volume_slider.value = GameDataManager.instance.get_setting("music_volume", 0.8)
	sfx_volume_slider.value = GameDataManager.instance.get_setting("sfx_volume", 1.0)
	
	# 加载显示设置
	fullscreen_checkbox.button_pressed = GameDataManager.instance.get_setting("fullscreen", false)
	vsync_checkbox.button_pressed = GameDataManager.instance.get_setting("vsync", true)
	
	# 加载游戏设置
	auto_save_checkbox.button_pressed = GameDataManager.instance.get_setting("auto_save_enabled", true)
	auto_save_interval_spinbox.value = GameDataManager.instance.get_setting("auto_save_interval", 5.0)
	
	# 更新显示值
	update_volume_labels()

func update_volume_labels():
	"""更新音量标签"""
	master_volume_value.text = "%d%%" % (master_volume_slider.value * 100)
	music_volume_value.text = "%d%%" % (music_volume_slider.value * 100)
	sfx_volume_value.text = "%d%%" % (sfx_volume_slider.value * 100)

func _on_confirmed():
	"""确认设置"""
	save_settings()
	hide()

func _on_canceled():
	"""取消设置"""
	load_current_settings()  # 恢复原始设置
	hide()

func save_settings():
	"""保存设置"""
	if not GameDataManager.instance:
		return
	
	print("[SettingsDialog] 保存设置")
	
	# 保存音频设置
	GameDataManager.instance.set_setting("master_volume", master_volume_slider.value)
	GameDataManager.instance.set_setting("music_volume", music_volume_slider.value)
	GameDataManager.instance.set_setting("sfx_volume", sfx_volume_slider.value)
	
	# 保存显示设置
	GameDataManager.instance.set_setting("fullscreen", fullscreen_checkbox.button_pressed)
	GameDataManager.instance.set_setting("vsync", vsync_checkbox.button_pressed)
	
	# 保存游戏设置
	GameDataManager.instance.set_setting("auto_save_enabled", auto_save_checkbox.button_pressed)
	GameDataManager.instance.set_setting("auto_save_interval", auto_save_interval_spinbox.value * 60.0)  # 转换为秒

# 音频设置回调
func _on_master_volume_changed(value: float):
	"""主音量变化"""
	master_volume_value.text = "%d%%" % (value * 100)
	# 实时应用设置
	if GameDataManager.instance:
		GameDataManager.instance.apply_setting("master_volume", value)

func _on_music_volume_changed(value: float):
	"""音乐音量变化"""
	music_volume_value.text = "%d%%" % (value * 100)
	# 实时应用设置
	if GameDataManager.instance:
		GameDataManager.instance.apply_setting("music_volume", value)

func _on_sfx_volume_changed(value: float):
	"""音效音量变化"""
	sfx_volume_value.text = "%d%%" % (value * 100)
	# 实时应用设置
	if GameDataManager.instance:
		GameDataManager.instance.apply_setting("sfx_volume", value)

# 显示设置回调
func _on_fullscreen_toggled(pressed: bool):
	"""全屏切换"""
	# 实时应用设置
	if GameDataManager.instance:
		GameDataManager.instance.apply_setting("fullscreen", pressed)

func _on_vsync_toggled(pressed: bool):
	"""垂直同步切换"""
	# 实时应用设置
	if GameDataManager.instance:
		GameDataManager.instance.apply_setting("vsync", pressed)

# 游戏设置回调
func _on_auto_save_toggled(pressed: bool):
	"""自动保存切换"""
	auto_save_interval_spinbox.editable = pressed

func _on_auto_save_interval_changed(value: float):
	"""自动保存间隔变化"""
	pass  # 在保存时统一处理
