# Country.gd
# 国家数据类 - 存储国家的政治、经济、社会信息
class_name Country
extends Resource

# 基础信息
@export var id: String = ""
@export var name: String = ""
@export var capital_city: String = ""
@export var flag_texture: Texture2D
@export var founding_date: String = ""

# 政治制度
@export var government_type: WorldData.GovernmentType = WorldData.GovernmentType.REPUBLIC
@export var political_stability: float = 75.0  # 政治稳定度 (0-100)
@export var corruption_index: float = 30.0     # 腐败指数 (0-100, 越低越好)

# 人口统计
@export var population: int = 1000000
@export var population_growth_rate: float = 0.02
@export var life_expectancy: float = 75.0
@export var literacy_rate: float = 95.0

# 经济数据
@export var gdp: float = 50000000.0
@export var gdp_growth_rate: float = 0.03
@export var inflation_rate: float = 0.02
@export var unemployment_rate: float = 0.05
@export var currency_name: String = "元"
@export var exchange_rate: float = 1.0  # 相对于基础货币的汇率

# 社会指标
@export var happiness_index: float = 65.0      # 幸福指数 (0-100)
@export var crime_rate: float = 15.0           # 犯罪率 (0-100)
@export var healthcare_quality: float = 70.0   # 医疗质量 (0-100)
@export var education_quality: float = 80.0    # 教育质量 (0-100)

# 基础设施
@export var infrastructure_level: float = 60.0  # 基础设施水平 (0-100)
@export var technology_level: float = 50.0      # 科技水平 (0-100)
@export var environmental_quality: float = 55.0 # 环境质量 (0-100)

# 财政数据
@export var government_budget: float = 5000000.0
@export var tax_revenue: float = 3000000.0
@export var government_debt: float = 10000000.0
@export var military_spending: float = 500000.0

# 政策和法律
@export var policies: Array[Policy] = []
@export var laws: Array[Law] = []

# 外交关系
@export var diplomatic_relations: Dictionary = {}  # 与其他国家的关系

# 自然资源
@export var natural_resources: Dictionary = {
	"oil": 0.0,
	"coal": 0.0,
	"iron": 0.0,
	"gold": 0.0,
	"agricultural_land": 0.0,
	"fresh_water": 0.0
}

func _init():
	"""初始化国家数据"""
	id = generate_unique_id()
	founding_date = Time.get_datetime_string_from_system()
	initialize_default_values()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "country_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func initialize_default_values():
	"""初始化默认值"""
	# 根据政体类型设置默认值
	match government_type:
		WorldData.GovernmentType.DEMOCRACY:
			political_stability = 80.0
			corruption_index = 25.0
			happiness_index = 70.0
		WorldData.GovernmentType.REPUBLIC:
			political_stability = 75.0
			corruption_index = 30.0
			happiness_index = 65.0
		WorldData.GovernmentType.MONARCHY:
			political_stability = 70.0
			corruption_index = 40.0
			happiness_index = 60.0
		WorldData.GovernmentType.FEDERATION:
			political_stability = 85.0
			corruption_index = 20.0
			happiness_index = 75.0
		WorldData.GovernmentType.AUTOCRACY:
			political_stability = 60.0
			corruption_index = 60.0
			happiness_index = 45.0

func update_economic_indicators():
	"""更新经济指标"""
	# 计算人均GDP
	var gdp_per_capita = gdp / population if population > 0 else 0.0
	
	# 根据各种因素调整经济增长
	var stability_factor = political_stability / 100.0
	var corruption_factor = (100.0 - corruption_index) / 100.0
	var infrastructure_factor = infrastructure_level / 100.0
	
	# 计算综合经济增长率
	var base_growth = 0.02
	gdp_growth_rate = base_growth * stability_factor * corruption_factor * infrastructure_factor
	
	# 更新GDP
	gdp *= (1.0 + gdp_growth_rate)
	
	# 更新人口
	population = int(population * (1.0 + population_growth_rate))

func add_policy(policy: Policy):
	"""添加政策"""
	policies.append(policy)
	apply_policy_effects(policy)

func remove_policy(policy_id: String) -> bool:
	"""移除政策"""
	for i in range(policies.size()):
		if policies[i].id == policy_id:
			remove_policy_effects(policies[i])
			policies.remove_at(i)
			return true
	return false

func apply_policy_effects(policy: Policy):
	"""应用政策效果"""
	match policy.type:
		Policy.PolicyType.ECONOMIC:
			gdp_growth_rate += policy.effect_value
		Policy.PolicyType.SOCIAL:
			happiness_index += policy.effect_value
		Policy.PolicyType.INFRASTRUCTURE:
			infrastructure_level += policy.effect_value
		Policy.PolicyType.EDUCATION:
			education_quality += policy.effect_value
		Policy.PolicyType.HEALTHCARE:
			healthcare_quality += policy.effect_value
		Policy.PolicyType.MILITARY:
			military_spending += policy.effect_value
		Policy.PolicyType.TAX:
			# 税收政策影响税收收入
			tax_revenue *= (1.0 + policy.effect_value)

func remove_policy_effects(policy: Policy):
	"""移除政策效果"""
	match policy.type:
		Policy.PolicyType.ECONOMIC:
			gdp_growth_rate -= policy.effect_value
		Policy.PolicyType.SOCIAL:
			happiness_index -= policy.effect_value
		Policy.PolicyType.INFRASTRUCTURE:
			infrastructure_level -= policy.effect_value
		Policy.PolicyType.EDUCATION:
			education_quality -= policy.effect_value
		Policy.PolicyType.HEALTHCARE:
			healthcare_quality -= policy.effect_value
		Policy.PolicyType.MILITARY:
			military_spending -= policy.effect_value
		Policy.PolicyType.TAX:
			tax_revenue /= (1.0 + policy.effect_value)

func set_diplomatic_relation(country_id: String, relation_value: float):
	"""设置外交关系"""
	diplomatic_relations[country_id] = clamp(relation_value, -100.0, 100.0)

func get_diplomatic_relation(country_id: String) -> float:
	"""获取外交关系"""
	return diplomatic_relations.get(country_id, 0.0)

func calculate_country_power() -> float:
	"""计算国家综合实力"""
	var economic_power = gdp / 1000000.0  # 经济实力
	var military_power = military_spending / 100000.0  # 军事实力
	var soft_power = (happiness_index + education_quality + healthcare_quality) / 3.0  # 软实力
	var tech_power = technology_level  # 科技实力
	
	return (economic_power + military_power + soft_power + tech_power) / 4.0

func get_government_type_name() -> String:
	"""获取政体类型名称"""
	match government_type:
		WorldData.GovernmentType.REPUBLIC:
			return "共和制"
		WorldData.GovernmentType.MONARCHY:
			return "君主制"
		WorldData.GovernmentType.FEDERATION:
			return "联邦制"
		WorldData.GovernmentType.DEMOCRACY:
			return "民主制"
		WorldData.GovernmentType.AUTOCRACY:
			return "专制"
		_:
			return "未知"

func get_country_summary() -> Dictionary:
	"""获取国家摘要信息"""
	return {
		"name": name,
		"government_type": get_government_type_name(),
		"population": population,
		"gdp": gdp,
		"gdp_per_capita": gdp / population if population > 0 else 0.0,
		"happiness_index": happiness_index,
		"political_stability": political_stability,
		"corruption_index": corruption_index,
		"unemployment_rate": unemployment_rate,
		"country_power": calculate_country_power()
	}

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"capital_city": capital_city,
		"government_type": government_type,
		"population": population,
		"gdp": gdp,
		"happiness_index": happiness_index,
		"political_stability": political_stability,
		"corruption_index": corruption_index,
		"founding_date": founding_date
	}
