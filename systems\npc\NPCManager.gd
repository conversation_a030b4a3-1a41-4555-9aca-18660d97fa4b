# NPCManager.gd
# NPC管理器 - 负责管理所有NPC的生命周期和行为
class_name NPCManager
extends Node

# 信号定义
signal npc_created(npc: NPC)
signal npc_destroyed(npc_id: String)
signal npc_behavior_changed(npc_id: String, new_behavior: String)

# NPC存储
var npcs: Dictionary = {}  # npc_id -> NPC
var npc_groups: Dictionary = {}  # group_name -> Array[NPC]

# 性能管理
var max_active_npcs: int = 1000
var lod_update_interval: float = 1.0
var lod_timer: float = 0.0

# LOD距离设置
var high_lod_distance: float = 200.0
var medium_lod_distance: float = 500.0
var low_lod_distance: float = 1000.0

# 统计数据
var total_npcs_created: int = 0
var active_npcs_count: int = 0

func _ready():
	"""初始化NPC管理器"""
	print("[NPCManager] NPC管理器初始化")
	set_process(true)

func _process(delta):
	"""每帧更新"""
	lod_timer += delta
	if lod_timer >= lod_update_interval:
		update_npc_lod()
		lod_timer = 0.0

func create_npc(npc_data: Dictionary) -> NPC:
	"""创建新的NPC"""
	var npc = NPC.new()
	npc.initialize_from_data(npc_data)
	
	# 添加到管理器
	npcs[npc.id] = npc
	total_npcs_created += 1
	active_npcs_count += 1
	
	# 添加到场景树
	add_child(npc)
	
	# 添加到组
	add_npc_to_group(npc, "all_npcs")
	
	# 发送信号
	npc_created.emit(npc)
	
	print("[NPCManager] 创建NPC: %s (%s)" % [npc.display_name, npc.id])
	return npc

func destroy_npc(npc_id: String) -> bool:
	"""销毁NPC"""
	var npc = npcs.get(npc_id)
	if npc:
		# 从组中移除
		remove_npc_from_all_groups(npc)
		
		# 从字典中移除
		npcs.erase(npc_id)
		active_npcs_count -= 1
		
		# 从场景树中移除
		npc.queue_free()
		
		# 发送信号
		npc_destroyed.emit(npc_id)
		
		print("[NPCManager] 销毁NPC: %s" % npc_id)
		return true
	
	return false

func get_npc(npc_id: String) -> NPC:
	"""获取NPC"""
	return npcs.get(npc_id)

func get_npcs_in_group(group_name: String) -> Array[NPC]:
	"""获取组中的所有NPC"""
	return npc_groups.get(group_name, [])

func add_npc_to_group(npc: NPC, group_name: String):
	"""将NPC添加到组"""
	if not npc_groups.has(group_name):
		npc_groups[group_name] = []
	
	if npc not in npc_groups[group_name]:
		npc_groups[group_name].append(npc)

func remove_npc_from_group(npc: NPC, group_name: String):
	"""从组中移除NPC"""
	if npc_groups.has(group_name):
		npc_groups[group_name].erase(npc)

func remove_npc_from_all_groups(npc: NPC):
	"""从所有组中移除NPC"""
	for group_name in npc_groups.keys():
		npc_groups[group_name].erase(npc)

func get_npcs_in_radius(center: Vector2, radius: float) -> Array[NPC]:
	"""获取指定半径内的所有NPC"""
	var result: Array[NPC] = []
	for npc in npcs.values():
		if npc.global_position.distance_to(center) <= radius:
			result.append(npc)
	return result

func get_npcs_by_profession(profession: String) -> Array[NPC]:
	"""根据职业获取NPC"""
	var result: Array[NPC] = []
	for npc in npcs.values():
		if npc.profession == profession:
			result.append(npc)
	return result

func update_npc_lod():
	"""更新NPC的LOD级别"""
	var player_position = get_player_position()
	if player_position == Vector2.ZERO:
		return
	
	for npc in npcs.values():
		var distance = npc.global_position.distance_to(player_position)
		var new_lod = calculate_lod_level(distance)
		npc.set_lod_level(new_lod)

func calculate_lod_level(distance: float) -> NPC.LODLevel:
	"""计算LOD级别"""
	if distance <= high_lod_distance:
		return NPC.LODLevel.HIGH
	elif distance <= medium_lod_distance:
		return NPC.LODLevel.MEDIUM
	elif distance <= low_lod_distance:
		return NPC.LODLevel.LOW
	else:
		return NPC.LODLevel.SLEEP

func get_player_position() -> Vector2:
	"""获取玩家位置"""
	var player = get_tree().get_first_node_in_group("player")
	if player:
		return player.global_position
	return Vector2.ZERO

func spawn_npcs_for_location(location_id: String, count: int):
	"""为指定位置生成NPC"""
	print("[NPCManager] 为位置 %s 生成 %d 个NPC" % [location_id, count])
	
	for i in range(count):
		var npc_data = generate_random_npc_data(location_id)
		var npc = create_npc(npc_data)
		
		# 设置位置
		npc.current_location_id = location_id
		
		# 添加到位置组
		add_npc_to_group(npc, "location_" + location_id)

func generate_random_npc_data(location_id: String) -> Dictionary:
	"""生成随机NPC数据"""
	var names = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"]
	var professions = ["工人", "教师", "医生", "商人", "农民", "学生", "退休人员"]
	
	return {
		"display_name": names[randi() % names.size()],
		"age": randi_range(18, 80),
		"profession": professions[randi() % professions.size()],
		"location_id": location_id,
		"needs": {
			"hunger": randf_range(50.0, 100.0),
			"energy": randf_range(50.0, 100.0),
			"social": randf_range(30.0, 70.0),
			"money": randf_range(0.0, 10000.0)
		},
		"personality": {
			"extroversion": randf_range(0.0, 1.0),
			"conscientiousness": randf_range(0.0, 1.0),
			"agreeableness": randf_range(0.0, 1.0)
		}
	}

func get_npc_statistics() -> Dictionary:
	"""获取NPC统计信息"""
	var stats = {
		"total_created": total_npcs_created,
		"currently_active": active_npcs_count,
		"by_profession": {},
		"by_lod_level": {
			"high": 0,
			"medium": 0,
			"low": 0,
			"sleep": 0
		},
		"average_happiness": 0.0
	}
	
	var total_happiness = 0.0
	
	for npc in npcs.values():
		# 统计职业分布
		var profession = npc.profession
		if not stats.by_profession.has(profession):
			stats.by_profession[profession] = 0
		stats.by_profession[profession] += 1
		
		# 统计LOD分布
		match npc.current_lod_level:
			NPC.LODLevel.HIGH:
				stats.by_lod_level.high += 1
			NPC.LODLevel.MEDIUM:
				stats.by_lod_level.medium += 1
			NPC.LODLevel.LOW:
				stats.by_lod_level.low += 1
			NPC.LODLevel.SLEEP:
				stats.by_lod_level.sleep += 1
		
		# 计算平均幸福度
		total_happiness += npc.get_happiness()
	
	if active_npcs_count > 0:
		stats.average_happiness = total_happiness / active_npcs_count
	
	return stats

func cleanup():
	"""清理所有NPC"""
	print("[NPCManager] 开始清理NPC...")
	
	for npc_id in npcs.keys():
		destroy_npc(npc_id)
	
	npc_groups.clear()
	total_npcs_created = 0
	active_npcs_count = 0
	
	print("[NPCManager] NPC清理完成")

func _exit_tree():
	"""节点退出场景树时的清理"""
	cleanup()
