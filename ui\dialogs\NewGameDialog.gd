# NewGameDialog.gd
# 新游戏设置对话框
extends AcceptDialog

# 信号定义
signal game_started(game_config: Dictionary)

# 节点引用
@onready var name_line_edit = $VBoxContainer/PlayerInfoContainer/PlayerNameContainer/NameLineEdit
@onready var age_spin_box = $VBoxContainer/PlayerInfoContainer/PlayerAgeContainer/AgeSpinBox
@onready var profession_option_button = $VBoxContainer/PlayerInfoContainer/PlayerProfessionContainer/ProfessionOptionButton
@onready var difficulty_option_button = $VBoxContainer/WorldSettingsContainer/DifficultyContainer/DifficultyOptionButton
@onready var economy_speed_slider = $VBoxContainer/WorldSettingsContainer/EconomySpeedContainer/EconomySpeedSlider
@onready var economy_speed_value_label = $VBoxContainer/WorldSettingsContainer/EconomySpeedContainer/EconomySpeedValueLabel

# 职业选项
var professions = [
	{"id": "student", "name": "学生", "description": "刚毕业的大学生，有学习能力但缺乏经验"},
	{"id": "worker", "name": "工人", "description": "有一定工作经验的普通工人"},
	{"id": "teacher", "name": "教师", "description": "教育工作者，有稳定收入和社会地位"},
	{"id": "businessman", "name": "商人", "description": "有商业头脑，善于赚钱但风险较高"},
	{"id": "doctor", "name": "医生", "description": "医疗专业人士，收入高但工作压力大"},
	{"id": "artist", "name": "艺术家", "description": "创意工作者，收入不稳定但有创造力"}
]

# 难度选项
var difficulties = [
	{"id": "easy", "name": "简单", "description": "适合新手，经济稳定，NPC友好"},
	{"id": "normal", "name": "普通", "description": "标准难度，平衡的游戏体验"},
	{"id": "hard", "name": "困难", "description": "挑战性强，经济波动大，竞争激烈"},
	{"id": "expert", "name": "专家", "description": "极具挑战性，适合有经验的玩家"}
]

func _ready():
	"""初始化对话框"""
	setup_ui()
	connect_signals()

func setup_ui():
	"""设置UI"""
	# 设置职业选项
	profession_option_button.clear()
	for profession in professions:
		profession_option_button.add_item(profession.name)
	profession_option_button.selected = 0
	
	# 设置难度选项
	difficulty_option_button.clear()
	for difficulty in difficulties:
		difficulty_option_button.add_item(difficulty.name)
	difficulty_option_button.selected = 1  # 默认选择普通难度
	
	# 设置经济速度显示
	update_economy_speed_label()

func connect_signals():
	"""连接信号"""
	confirmed.connect(_on_confirmed)
	canceled.connect(_on_canceled)

func show_dialog():
	"""显示对话框"""
	popup_centered()

func _on_confirmed():
	"""确认按钮点击"""
	var game_config = collect_game_config()
	
	# 验证输入
	if validate_config(game_config):
		game_started.emit(game_config)
		hide()
	else:
		show_validation_error()

func _on_canceled():
	"""取消按钮点击"""
	hide()

func collect_game_config() -> Dictionary:
	"""收集游戏配置"""
	var config = {
		"player_name": name_line_edit.text.strip_edges(),
		"player_age": int(age_spin_box.value),
		"player_profession": professions[profession_option_button.selected].id,
		"difficulty": difficulties[difficulty_option_button.selected].id,
		"economy_speed": economy_speed_slider.value,
		"world_name": "新世界",
		"creation_time": Time.get_datetime_string_from_system()
	}
	
	return config

func validate_config(config: Dictionary) -> bool:
	"""验证配置"""
	# 检查角色姓名
	if config.player_name.is_empty():
		return false
	
	# 检查年龄范围
	if config.player_age < 18 or config.player_age > 80:
		return false
	
	return true

func show_validation_error():
	"""显示验证错误"""
	var error_dialog = AcceptDialog.new()
	error_dialog.title = "输入错误"
	error_dialog.dialog_text = "请检查输入信息：\n- 角色姓名不能为空\n- 年龄必须在18-80岁之间"
	add_child(error_dialog)
	error_dialog.popup_centered()
	
	# 自动清理
	error_dialog.confirmed.connect(func(): error_dialog.queue_free())

func _on_economy_speed_changed(value: float):
	"""经济速度滑块变化"""
	update_economy_speed_label()

func update_economy_speed_label():
	"""更新经济速度标签"""
	economy_speed_value_label.text = "%.1fx" % economy_speed_slider.value

# 获取选中的职业信息
func get_selected_profession() -> Dictionary:
	"""获取选中的职业信息"""
	return professions[profession_option_button.selected]

# 获取选中的难度信息
func get_selected_difficulty() -> Dictionary:
	"""获取选中的难度信息"""
	return difficulties[difficulty_option_button.selected]
