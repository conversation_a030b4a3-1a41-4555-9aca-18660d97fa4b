# Law.gd
# 法律类 - 表示游戏中的法律法规
class_name Law
extends Resource

# 法律类型枚举
enum LawType {
	CRIMINAL,       # 刑法
	CIVIL,          # 民法
	COMMERCIAL,     # 商法
	CONSTITUTIONAL, # 宪法
	ADMINISTRATIVE, # 行政法
	LABOR,          # 劳动法
	ENVIRONMENTAL,  # 环境法
	TAX,           # 税法
	PROPERTY,      # 物权法
	FAMILY         # 家庭法
}

# 法律严格程度枚举
enum Severity {
	LENIENT,    # 宽松
	MODERATE,   # 适中
	STRICT,     # 严格
	HARSH       # 严厉
}

# 基础信息
@export var id: String = ""
@export var name: String = ""
@export var description: String = ""
@export var type: LawType = LawType.CIVIL
@export var severity: Severity = Severity.MODERATE

# 法律条款
@export var articles: Array[String] = []
@export var penalties: Dictionary = {}  # 违法行为 -> 惩罚
@export var enforcement_level: float = 50.0  # 执法力度 (0-100)

# 实施信息
@export var enactment_date: String = ""
@export var is_active: bool = false
@export var public_support: float = 50.0

# 影响范围
@export var jurisdiction: Array[String] = []  # 管辖区域

func _init():
	"""初始化法律"""
	id = generate_unique_id()
	enactment_date = Time.get_datetime_string_from_system()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "law_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func enact():
	"""颁布法律"""
	is_active = true
	enactment_date = Time.get_datetime_string_from_system()
	print("[Law] 法律 '%s' 已颁布" % name)

func repeal():
	"""废除法律"""
	is_active = false
	print("[Law] 法律 '%s' 已废除" % name)

func add_penalty(violation: String, punishment: String):
	"""添加惩罚条款"""
	penalties[violation] = punishment

func get_penalty(violation: String) -> String:
	"""获取违法行为的惩罚"""
	return penalties.get(violation, "无相关条款")

func get_law_type_name() -> String:
	"""获取法律类型名称"""
	match type:
		LawType.CRIMINAL:
			return "刑法"
		LawType.CIVIL:
			return "民法"
		LawType.COMMERCIAL:
			return "商法"
		LawType.CONSTITUTIONAL:
			return "宪法"
		LawType.ADMINISTRATIVE:
			return "行政法"
		LawType.LABOR:
			return "劳动法"
		LawType.ENVIRONMENTAL:
			return "环境法"
		LawType.TAX:
			return "税法"
		LawType.PROPERTY:
			return "物权法"
		LawType.FAMILY:
			return "家庭法"
		_:
			return "未知法律"

func get_severity_name() -> String:
	"""获取严格程度名称"""
	match severity:
		Severity.LENIENT:
			return "宽松"
		Severity.MODERATE:
			return "适中"
		Severity.STRICT:
			return "严格"
		Severity.HARSH:
			return "严厉"
		_:
			return "未知"

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"description": description,
		"type": type,
		"severity": severity,
		"is_active": is_active,
		"public_support": public_support,
		"enforcement_level": enforcement_level,
		"enactment_date": enactment_date
	}
