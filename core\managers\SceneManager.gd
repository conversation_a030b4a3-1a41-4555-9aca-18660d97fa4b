# SceneManager.gd
# 场景管理器 - 负责场景切换和加载
class_name SceneManager
extends Node

# 信号定义
signal scene_loading_started(scene_path: String)
signal scene_loading_progress(progress: float)
signal scene_loading_finished(scene_path: String)
signal scene_changed(old_scene: String, new_scene: String)

# 单例实例
static var instance: SceneManager

# 场景路径常量
const SCENES = {
	"main_menu": "res://scenes/MainMenu.tscn",
	"game_world": "res://scenes/GameWorld.tscn",
	"settings": "res://scenes/Settings.tscn",
	"loading": "res://scenes/Loading.tscn"
}

# 当前场景信息
var current_scene: Node
var current_scene_path: String = ""
var is_loading: bool = false

# 加载相关
var loading_thread: Thread
var loading_progress: float = 0.0

func _ready():
	# 确保单例模式
	if instance == null:
		instance = self
		initialize_manager()
	else:
		queue_free()

func initialize_manager():
	"""初始化场景管理器"""
	print("[SceneManager] 场景管理器初始化")
	
	# 获取当前场景
	current_scene = get_tree().current_scene
	if current_scene:
		current_scene_path = current_scene.scene_file_path
	
	# 连接树信号
	get_tree().node_added.connect(_on_node_added)

func change_scene(scene_key: String, use_loading_screen: bool = true):
	"""切换场景"""
	if is_loading:
		print("[SceneManager] 正在加载场景，忽略切换请求")
		return
	
	var scene_path = SCENES.get(scene_key, scene_key)
	if scene_path == current_scene_path:
		print("[SceneManager] 目标场景与当前场景相同，忽略切换")
		return
	
	print("[SceneManager] 开始切换场景: %s" % scene_path)
	
	if use_loading_screen and scene_key != "loading":
		# 使用加载屏幕
		await change_to_loading_screen()
		await load_scene_async(scene_path)
	else:
		# 直接切换
		load_scene_sync(scene_path)

func change_to_loading_screen():
	"""切换到加载屏幕"""
	if current_scene_path != SCENES.loading:
		load_scene_sync(SCENES.loading)
		await get_tree().process_frame

func load_scene_sync(scene_path: String):
	"""同步加载场景"""
	print("[SceneManager] 同步加载场景: %s" % scene_path)
	
	var old_scene_path = current_scene_path
	
	# 释放当前场景
	if current_scene:
		current_scene.queue_free()
		await current_scene.tree_exited
	
	# 加载新场景
	var new_scene = load(scene_path)
	if new_scene:
		current_scene = new_scene.instantiate()
		get_tree().root.add_child(current_scene)
		get_tree().current_scene = current_scene
		current_scene_path = scene_path
		
		scene_changed.emit(old_scene_path, scene_path)
		print("[SceneManager] 场景切换完成: %s" % scene_path)
	else:
		print("[SceneManager] 场景加载失败: %s" % scene_path)

func load_scene_async(scene_path: String):
	"""异步加载场景"""
	if is_loading:
		return
	
	is_loading = true
	scene_loading_started.emit(scene_path)
	print("[SceneManager] 开始异步加载场景: %s" % scene_path)
	
	# 开始异步加载
	var error = ResourceLoader.load_threaded_request(scene_path)
	if error != OK:
		print("[SceneManager] 异步加载请求失败: %s" % scene_path)
		is_loading = false
		return
	
	# 等待加载完成
	while true:
		var progress = []
		var status = ResourceLoader.load_threaded_get_status(scene_path, progress)
		
		if progress.size() > 0:
			loading_progress = progress[0]
			scene_loading_progress.emit(loading_progress)
		
		match status:
			ResourceLoader.THREAD_LOAD_LOADED:
				# 加载完成
				var new_scene_resource = ResourceLoader.load_threaded_get(scene_path)
				if new_scene_resource:
					await switch_to_loaded_scene(new_scene_resource, scene_path)
				break
			ResourceLoader.THREAD_LOAD_FAILED:
				print("[SceneManager] 异步加载失败: %s" % scene_path)
				break
			ResourceLoader.THREAD_LOAD_INVALID_RESOURCE:
				print("[SceneManager] 无效资源: %s" % scene_path)
				break
		
		await get_tree().process_frame
	
	is_loading = false
	scene_loading_finished.emit(scene_path)

func switch_to_loaded_scene(scene_resource: PackedScene, scene_path: String):
	"""切换到已加载的场景"""
	var old_scene_path = current_scene_path
	
	# 释放当前场景
	if current_scene and current_scene_path != SCENES.loading:
		current_scene.queue_free()
		await current_scene.tree_exited
	
	# 实例化新场景
	var new_scene = scene_resource.instantiate()
	if new_scene:
		# 如果当前是加载屏幕，替换它
		if current_scene_path == SCENES.loading:
			current_scene.queue_free()
			await current_scene.tree_exited
		
		get_tree().root.add_child(new_scene)
		get_tree().current_scene = new_scene
		current_scene = new_scene
		current_scene_path = scene_path
		
		scene_changed.emit(old_scene_path, scene_path)
		print("[SceneManager] 异步场景切换完成: %s" % scene_path)

func get_current_scene() -> Node:
	"""获取当前场景"""
	return current_scene

func get_current_scene_path() -> String:
	"""获取当前场景路径"""
	return current_scene_path

func is_scene_loading() -> bool:
	"""检查是否正在加载场景"""
	return is_loading

func get_loading_progress() -> float:
	"""获取加载进度"""
	return loading_progress

# 便捷方法
func go_to_main_menu():
	"""返回主菜单"""
	change_scene("main_menu")

func start_game():
	"""开始游戏"""
	change_scene("game_world")

func open_settings():
	"""打开设置"""
	change_scene("settings")

func quit_game():
	"""退出游戏"""
	print("[SceneManager] 退出游戏")
	get_tree().quit()

# 信号回调
func _on_node_added(node: Node):
	"""节点添加到场景树时的回调"""
	if node == get_tree().current_scene:
		current_scene = node

func _exit_tree():
	"""清理资源"""
	if loading_thread and loading_thread.is_started():
		loading_thread.wait_to_finish()
	
	print("[SceneManager] 场景管理器清理完成")
