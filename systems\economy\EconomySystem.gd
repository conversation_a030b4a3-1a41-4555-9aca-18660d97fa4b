# EconomySystem.gd
# 经济系统 - 管理游戏中的经济活动
class_name EconomySystem
extends Node

# 信号定义
signal price_changed(good_id: String, new_price: float)
signal market_update(market_data: Dictionary)
signal transaction_completed(transaction: Dictionary)

# 市场数据
var markets: Dictionary = {}  # region_id -> market_data
var goods_database: Dictionary = {}
var companies: Array[Company] = []
var price_history: Dictionary = {}

# 经济参数
var inflation_rate: float = 0.02
var market_volatility: float = 0.1
var update_interval: float = 5.0  # 市场更新间隔（秒）

func _ready():
	"""初始化经济系统"""
	print("[EconomySystem] 经济系统初始化")
	initialize_goods_database()
	setup_market_timer()

func initialize_goods_database():
	"""初始化商品数据库"""
	goods_database = {
		"food": {
			"name": "食物",
			"base_price": 10.0,
			"category": "necessity",
			"perishable": true
		},
		"clothing": {
			"name": "服装",
			"base_price": 50.0,
			"category": "necessity",
			"perishable": false
		},
		"housing": {
			"name": "住房",
			"base_price": 1000.0,
			"category": "necessity",
			"perishable": false
		},
		"electronics": {
			"name": "电子产品",
			"base_price": 500.0,
			"category": "luxury",
			"perishable": false
		}
	}

func setup_market_timer():
	"""设置市场更新定时器"""
	var timer = Timer.new()
	timer.wait_time = update_interval
	timer.timeout.connect(update_markets)
	add_child(timer)
	timer.start()

func update_markets():
	"""更新所有市场"""
	for region_id in markets.keys():
		update_market(region_id)
	
	market_update.emit(get_market_summary())

func update_market(region_id: String):
	"""更新指定地区的市场"""
	var market = markets.get(region_id, {})
	
	for good_id in goods_database.keys():
		var new_price = calculate_market_price(good_id, region_id)
		var old_price = market.get("price_" + good_id, 0.0)
		
		if abs(new_price - old_price) > 0.01:
			market["price_" + good_id] = new_price
			price_changed.emit(good_id, new_price)
			record_price_history(good_id, region_id, new_price)

func calculate_market_price(good_id: String, region_id: String) -> float:
	"""计算市场价格"""
	var market = markets.get(region_id, {})
	var supply = market.get("supply_" + good_id, 100.0)
	var demand = market.get("demand_" + good_id, 100.0)
	var base_price = goods_database[good_id].base_price
	
	# 供需比例影响价格
	var supply_demand_ratio = demand / max(supply, 1.0)
	var price_modifier = supply_demand_ratio
	
	# 添加市场波动
	var volatility_factor = 1.0 + randf_range(-market_volatility, market_volatility)
	
	# 通胀影响
	var inflation_factor = 1.0 + inflation_rate
	
	return base_price * price_modifier * volatility_factor * inflation_factor

func record_price_history(good_id: String, region_id: String, price: float):
	"""记录价格历史"""
	var key = good_id + "_" + region_id
	if not price_history.has(key):
		price_history[key] = []
	
	price_history[key].append({
		"price": price,
		"timestamp": Time.get_ticks_msec()
	})
	
	# 限制历史记录长度
	if price_history[key].size() > 100:
		price_history[key].pop_front()

func get_market_summary() -> Dictionary:
	"""获取市场摘要"""
	var summary = {
		"total_markets": markets.size(),
		"average_prices": {},
		"price_trends": {}
	}
	
	for good_id in goods_database.keys():
		var total_price = 0.0
		var market_count = 0
		
		for region_id in markets.keys():
			var market = markets[region_id]
			var price = market.get("price_" + good_id, 0.0)
			if price > 0:
				total_price += price
				market_count += 1
		
		if market_count > 0:
			summary.average_prices[good_id] = total_price / market_count
	
	return summary

func cleanup():
	"""清理经济系统"""
	markets.clear()
	companies.clear()
	price_history.clear()
	print("[EconomySystem] 经济系统清理完成")
