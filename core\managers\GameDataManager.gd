# GameDataManager.gd
# 游戏数据管理器 - 负责管理所有游戏数据和核心系统
class_name GameDataManager
extends Node

# 信号定义
signal data_changed(key: String, value)
signal system_initialized(system_name: String)
signal game_state_changed(new_state: GameState)

# 游戏状态枚举
enum GameState {
	MENU,
	LOADING,
	PLAYING,
	PAUSED,
	SETTINGS
}

# 单例实例
static var instance: GameDataManager

# 核心系统引用
var world_data: WorldData
var npc_manager: NPCManager
var economy_system: EconomySystem
var time_system: TimeSystem
var save_system: SaveSystem

# 游戏状态
var current_game_state: GameState = GameState.MENU
var is_initialized: bool = false

# 游戏数据
var game_data: Dictionary = {}
var player_data: Dictionary = {}
var settings_data: Dictionary = {}

func _ready():
	# 确保单例模式
	if instance == null:
		instance = self
		initialize_manager()
	else:
		queue_free()

func initialize_manager():
	"""初始化游戏数据管理器"""
	print("[GameDataManager] 开始初始化...")
	
	# 加载默认设置
	load_default_settings()
	
	# 初始化核心系统
	await initialize_systems()
	
	# 标记为已初始化
	is_initialized = true
	print("[GameDataManager] 初始化完成")

func load_default_settings():
	"""加载默认设置"""
	settings_data = {
		"master_volume": 1.0,
		"music_volume": 0.8,
		"sfx_volume": 1.0,
		"fullscreen": false,
		"vsync": true,
		"language": "zh_CN"
	}

func initialize_systems():
	"""初始化所有核心系统"""
	print("[GameDataManager] 初始化核心系统...")
	
	# 创建系统实例
	world_data = WorldData.new()
	npc_manager = NPCManager.new()
	economy_system = EconomySystem.new()
	time_system = TimeSystem.new()
	save_system = SaveSystem.new()
	
	# 添加到场景树
	add_child(npc_manager)
	add_child(economy_system)
	add_child(time_system)
	add_child(save_system)
	
	# 等待系统初始化
	await get_tree().process_frame
	
	# 连接系统信号
	connect_system_signals()
	
	print("[GameDataManager] 核心系统初始化完成")

func connect_system_signals():
	"""连接系统信号"""
	if time_system:
		time_system.hour_passed.connect(_on_hour_passed)
		time_system.day_passed.connect(_on_day_passed)
	
	if economy_system:
		economy_system.price_changed.connect(_on_price_changed)

# 游戏状态管理
func set_game_state(new_state: GameState):
	"""设置游戏状态"""
	if current_game_state != new_state:
		var old_state = current_game_state
		current_game_state = new_state
		game_state_changed.emit(new_state)
		print("[GameDataManager] 游戏状态变更: %s -> %s" % [GameState.keys()[old_state], GameState.keys()[new_state]])

func get_game_state() -> GameState:
	"""获取当前游戏状态"""
	return current_game_state

# 数据管理方法
func set_data(key: String, value):
	"""设置游戏数据"""
	game_data[key] = value
	data_changed.emit(key, value)

func get_data(key: String, default_value = null):
	"""获取游戏数据"""
	return game_data.get(key, default_value)

func has_data(key: String) -> bool:
	"""检查是否存在指定数据"""
	return game_data.has(key)

# 玩家数据管理
func set_player_data(key: String, value):
	"""设置玩家数据"""
	player_data[key] = value

func get_player_data(key: String, default_value = null):
	"""获取玩家数据"""
	return player_data.get(key, default_value)

# 设置管理
func set_setting(key: String, value):
	"""设置游戏设置"""
	settings_data[key] = value
	apply_setting(key, value)

func get_setting(key: String, default_value = null):
	"""获取游戏设置"""
	return settings_data.get(key, default_value)

func apply_setting(key: String, value):
	"""应用设置"""
	match key:
		"master_volume":
			AudioServer.set_bus_volume_db(0, linear_to_db(value))
		"fullscreen":
			if value:
				DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
			else:
				DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
		"vsync":
			if value:
				DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_ENABLED)
			else:
				DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_DISABLED)

# 系统访问方法
func get_npc_manager() -> NPCManager:
	"""获取NPC管理器"""
	return npc_manager

func get_economy_system() -> EconomySystem:
	"""获取经济系统"""
	return economy_system

func get_time_system() -> TimeSystem:
	"""获取时间系统"""
	return time_system

func get_world_data() -> WorldData:
	"""获取世界数据"""
	return world_data

# 信号回调
func _on_hour_passed(hour: int):
	"""小时变化回调"""
	print("[GameDataManager] 时间: %d:00" % hour)

func _on_day_passed(day: int):
	"""天数变化回调"""
	print("[GameDataManager] 新的一天: 第%d天" % day)

func _on_price_changed(good_id: String, new_price: float):
	"""价格变化回调"""
	print("[GameDataManager] 商品价格变化: %s = %.2f" % [good_id, new_price])

# 清理方法
func cleanup():
	"""清理资源"""
	if npc_manager:
		npc_manager.cleanup()
	if economy_system:
		economy_system.cleanup()
	if time_system:
		time_system.cleanup()
	
	game_data.clear()
	player_data.clear()
	
	print("[GameDataManager] 资源清理完成")

func _exit_tree():
	"""节点退出场景树时的清理"""
	cleanup()
