# NPC.gd
# NPC基础类 - 表示游戏中的非玩家角色
class_name NPC
extends CharacterBody2D

# 信号定义
signal behavior_changed(new_behavior: String)
signal need_changed(need_name: String, new_value: float)
signal location_changed(old_location: String, new_location: String)

# LOD级别枚举
enum LODLevel {
	HIGH,    # 详细模拟
	MEDIUM,  # 简化模拟
	LOW,     # 统计模拟
	SLEEP    # 休眠状态
}

# 基础信息
@export var id: String = ""
@export var display_name: String = "NPC"
@export var age: int = 25
@export var profession: String = "学生"
@export var current_location_id: String = ""

# 需求系统
@export var needs: Dictionary = {
	"hunger": 100.0,      # 饥饿值 (0-100)
	"energy": 100.0,      # 精力值 (0-100)
	"social": 50.0,       # 社交需求 (0-100)
	"money": 1000.0,      # 金钱
	"happiness": 50.0,    # 幸福度 (0-100)
	"health": 100.0       # 健康值 (0-100)
}

# 个性特征
@export var personality: Dictionary = {
	"extroversion": 0.5,      # 外向性 (0-1)
	"conscientiousness": 0.5, # 尽责性 (0-1)
	"agreeableness": 0.5,     # 宜人性 (0-1)
	"neuroticism": 0.5,       # 神经质 (0-1)
	"openness": 0.5           # 开放性 (0-1)
}

# 技能和属性
@export var skills: Dictionary = {
	"intelligence": 50.0,     # 智力 (0-100)
	"charisma": 50.0,        # 魅力 (0-100)
	"strength": 50.0,        # 力量 (0-100)
	"creativity": 50.0,      # 创造力 (0-100)
	"business": 50.0         # 商业能力 (0-100)
}

# 关系网络
@export var relationships: Dictionary = {}  # npc_id -> relationship_value (-100 to 100)

# 行为相关
var current_behavior: String = "idle"
var current_lod_level: LODLevel = LODLevel.HIGH
var behavior_tree: Node

# 日程安排
var daily_schedule: Array[Dictionary] = []
var current_activity: Dictionary = {}

# 移动相关
@export var move_speed: float = 100.0
var target_position: Vector2 = Vector2.ZERO
var is_moving: bool = false

func _ready():
	"""初始化NPC"""
	if id.is_empty():
		id = generate_unique_id()
	
	setup_behavior_tree()
	initialize_daily_schedule()
	
	print("[NPC] %s (%s) 初始化完成" % [display_name, id])

func _physics_process(delta):
	"""物理更新"""
	if current_lod_level == LODLevel.HIGH:
		# 高精度模拟
		update_movement(delta)
		update_needs(delta)
	elif current_lod_level == LODLevel.MEDIUM:
		# 中等精度模拟
		update_needs_simplified(delta)
	# LOW和SLEEP级别不进行实时更新

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "npc_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func initialize_from_data(data: Dictionary):
	"""从数据初始化NPC"""
	display_name = data.get("display_name", "NPC")
	age = data.get("age", 25)
	profession = data.get("profession", "学生")
	current_location_id = data.get("location_id", "")
	
	if data.has("needs"):
		needs = data.needs.duplicate()
	
	if data.has("personality"):
		personality = data.personality.duplicate()
	
	if data.has("skills"):
		skills = data.skills.duplicate()

func setup_behavior_tree():
	"""设置行为树"""
	# 这里将来会集成Beehave行为树
	pass

func initialize_daily_schedule():
	"""初始化日程安排"""
	daily_schedule = [
		{"time": 8, "activity": "wake_up", "location": "home"},
		{"time": 9, "activity": "work", "location": "workplace"},
		{"time": 12, "activity": "lunch", "location": "restaurant"},
		{"time": 13, "activity": "work", "location": "workplace"},
		{"time": 18, "activity": "dinner", "location": "home"},
		{"time": 20, "activity": "leisure", "location": "home"},
		{"time": 22, "activity": "sleep", "location": "home"}
	]

func update_movement(delta):
	"""更新移动"""
	if is_moving and target_position != Vector2.ZERO:
		var direction = (target_position - global_position).normalized()
		var distance = global_position.distance_to(target_position)
		
		if distance > 5.0:
			velocity = direction * move_speed
			move_and_slide()
		else:
			velocity = Vector2.ZERO
			is_moving = false
			target_position = Vector2.ZERO

func update_needs(delta):
	"""更新需求值"""
	var time_factor = delta / 60.0  # 每分钟的变化
	
	# 基础需求衰减
	needs.hunger = max(0.0, needs.hunger - 10.0 * time_factor)
	needs.energy = max(0.0, needs.energy - 5.0 * time_factor)
	needs.social = max(0.0, needs.social - 2.0 * time_factor)
	
	# 根据活动调整需求
	adjust_needs_by_activity(time_factor)
	
	# 计算幸福度
	calculate_happiness()

func update_needs_simplified(delta):
	"""简化的需求更新"""
	var time_factor = delta / 60.0
	
	# 只更新关键需求
	needs.hunger = max(0.0, needs.hunger - 5.0 * time_factor)
	needs.energy = max(0.0, needs.energy - 2.5 * time_factor)

func adjust_needs_by_activity(time_factor: float):
	"""根据当前活动调整需求"""
	match current_behavior:
		"eating":
			needs.hunger = min(100.0, needs.hunger + 30.0 * time_factor)
		"sleeping":
			needs.energy = min(100.0, needs.energy + 40.0 * time_factor)
		"socializing":
			needs.social = min(100.0, needs.social + 20.0 * time_factor)
		"working":
			needs.energy = max(0.0, needs.energy - 15.0 * time_factor)
			needs.money += 10.0 * time_factor

func calculate_happiness():
	"""计算幸福度"""
	var base_happiness = 50.0
	
	# 需求满足度影响
	var need_satisfaction = (needs.hunger + needs.energy + needs.social + needs.health) / 4.0
	
	# 金钱影响（对数函数，边际效用递减）
	var money_factor = min(50.0, log(max(1.0, needs.money)) * 10.0)
	
	# 个性影响
	var personality_factor = personality.extroversion * 10.0 + personality.agreeableness * 5.0
	
	needs.happiness = clamp(base_happiness + (need_satisfaction - 50.0) * 0.5 + money_factor * 0.2 + personality_factor, 0.0, 100.0)

func set_lod_level(new_lod: LODLevel):
	"""设置LOD级别"""
	if current_lod_level != new_lod:
		current_lod_level = new_lod
		apply_lod_settings()

func apply_lod_settings():
	"""应用LOD设置"""
	match current_lod_level:
		LODLevel.HIGH:
			visible = true
			set_physics_process(true)
		LODLevel.MEDIUM:
			visible = true
			set_physics_process(true)
		LODLevel.LOW:
			visible = false
			set_physics_process(false)
		LODLevel.SLEEP:
			visible = false
			set_physics_process(false)

func move_to_location(location_id: String):
	"""移动到指定位置"""
	var old_location = current_location_id
	current_location_id = location_id
	location_changed.emit(old_location, location_id)
	
	# 这里应该获取位置坐标并设置目标
	# target_position = get_location_position(location_id)
	# is_moving = true

func set_behavior(new_behavior: String):
	"""设置行为"""
	if current_behavior != new_behavior:
		current_behavior = new_behavior
		behavior_changed.emit(new_behavior)

func get_happiness() -> float:
	"""获取幸福度"""
	return needs.happiness

func get_relationship(other_npc_id: String) -> float:
	"""获取与其他NPC的关系"""
	return relationships.get(other_npc_id, 0.0)

func set_relationship(other_npc_id: String, value: float):
	"""设置与其他NPC的关系"""
	relationships[other_npc_id] = clamp(value, -100.0, 100.0)

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"display_name": display_name,
		"age": age,
		"profession": profession,
		"current_location_id": current_location_id,
		"needs": needs.duplicate(),
		"personality": personality.duplicate(),
		"skills": skills.duplicate(),
		"relationships": relationships.duplicate(),
		"current_behavior": current_behavior,
		"position": {"x": global_position.x, "y": global_position.y}
	}
