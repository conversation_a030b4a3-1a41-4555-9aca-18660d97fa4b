# LifeD - 生活模拟沙盒游戏

## 📖 项目简介

LifeD是一款结合模拟经营、视觉小说的沙盒类游戏，基于Godot 4.4引擎和GDScript开发。游戏特色包括：

- **多层级世界系统**: 世界→国家→省份→城市→区域→村庄的完整行政区划
- **NPC个体行为系统**: 每个NPC都有独立的需求、个性和行为模式
- **动态经济系统**: 基于供需关系的真实经济模拟
- **回合制时间系统**: 24小时制的时间推进和事件调度
- **AI驱动内容**: 集成LLM、文生图等AI技术生成动态内容

## 🏗️ 项目架构

### 核心架构分层
```
┌─────────────────────────────────────┐
│           AI演出层 (Presentation)    │
│  ┌─────────┬─────────┬─────────────┐ │
│  │   LLM   │ 文生图  │    TTS      │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│          逻辑层 (Logic Layer)        │
│  ┌─────────┬─────────┬─────────────┐ │
│  │ 经济系统 │NPC行为  │  时间系统   │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│         数据层 (Data Layer)          │
│  ┌─────────┬─────────┬─────────────┐ │
│  │ 地图数据 │NPC数据  │  游戏状态   │ │
│  └─────────┴─────────┴─────────────┘ │
└─────────────────────────────────────┘
```

### 文件结构
```
res://
├── core/                 # 核心系统
│   ├── managers/         # 管理器类
│   │   ├── GameDataManager.gd
│   │   └── SceneManager.gd
│   ├── data/            # 数据定义
│   │   ├── WorldData.gd
│   │   ├── MapNode.gd
│   │   ├── Country.gd
│   │   ├── NPC.gd
│   │   ├── Building.gd
│   │   ├── Policy.gd
│   │   ├── Law.gd
│   │   ├── Company.gd
│   │   └── SaveSystem.gd
│   └── utils/           # 工具类
├── systems/             # 各子系统
│   ├── npc/            # NPC系统
│   │   └── NPCManager.gd
│   ├── economy/        # 经济系统
│   │   └── EconomySystem.gd
│   ├── map/            # 地图系统
│   └── time/           # 时间系统
│       └── TimeSystem.gd
├── ui/                  # 用户界面
│   ├── menus/          # 菜单界面
│   │   └── MainMenu.gd
│   ├── dialogs/        # 对话框
│   │   ├── NewGameDialog.tscn/.gd
│   │   ├── LoadGameDialog.tscn/.gd
│   │   └── SettingsDialog.tscn/.gd
│   └── hud/            # 游戏HUD
├── scenes/              # 场景文件
│   ├── MainMenu.tscn
│   └── GameWorld.tscn/.gd
├── resources/           # 资源文件
└── addons/              # 插件
```

## ✅ 已完成功能

### 第一阶段：核心架构搭建 ✅
- [x] 项目基础结构创建
- [x] 核心管理器系统 (GameDataManager, SceneManager)
- [x] 基础数据类定义 (WorldData, MapNode, Country, NPC等)
- [x] 主菜单系统完整实现
- [x] 设置系统和对话框
- [x] 存档系统基础框架
- [x] 时间系统核心功能
- [x] NPC管理器和基础行为
- [x] 经济系统框架

### 核心系统功能

#### 🎮 主菜单系统
- 完整的主菜单界面
- 新游戏创建向导
- 存档加载和管理
- 游戏设置（音频、显示、游戏选项）
- 响应式UI设计

#### 🗺️ 多层级地图系统
- 6层级地图结构：世界→国家→省份→城市→区域→村庄
- 建筑空间管理系统
- 行政区划和政策管理
- 地图数据序列化

#### 🤖 NPC个体行为系统
- 个体需求系统（饥饿、精力、社交、金钱、幸福度、健康）
- 个性特征系统（五大人格特质）
- 技能和关系网络
- LOD性能优化系统
- 日程安排和行为状态机

#### 💰 经济系统
- 供需驱动的动态价格机制
- 商品数据库和市场管理
- 价格历史记录
- 公司和组织系统
- 经济指标计算

#### ⏰ 时间系统
- 24小时回合制时间推进
- 时间加速和暂停功能
- 事件调度系统
- 工作时间和周末判断

#### 💾 存档系统
- JSON格式存档
- 自动保存功能
- 存档列表管理
- 增量保存优化

## 🔧 技术特性

### 性能优化
- **LOD系统**: 根据距离动态调整NPC模拟精度
- **分帧处理**: 昂贵操作分散到多帧执行
- **对象池**: 重用频繁创建的对象
- **缓存机制**: 减少重复计算和查找

### 数据管理
- **模块化架构**: 各系统独立但通过事件通信
- **类型安全**: 使用GDScript类型注解
- **资源管理**: 自动内存管理和资源清理
- **配置系统**: 灵活的游戏设置管理

### 扩展性设计
- **插件接口**: 为AI功能预留接口
- **事件驱动**: 松耦合的系统通信
- **数据驱动**: 配置文件驱动的游戏内容
- **模板系统**: 可复用的游戏对象模板

## 🎯 下一步开发计划

### 第二阶段：地图系统实现 (进行中)
- [ ] 地图可视化和渲染
- [ ] 世界生成算法
- [ ] 建筑放置和管理
- [ ] 地图编辑工具

### 第三阶段：NPC行为增强
- [ ] 集成Beehave行为树插件
- [ ] 复杂AI决策逻辑
- [ ] NPC社交互动系统
- [ ] 职业和技能系统

### 第四阶段：经济系统完善
- [ ] 复杂的市场机制
- [ ] 公司经营玩法
- [ ] 投资和金融系统
- [ ] 贸易和物流

### 第五阶段：AI集成
- [ ] LLM对话生成
- [ ] 文生图角色立绘
- [ ] TTS语音合成
- [ ] 动态剧情生成

## 🚀 运行要求

- **引擎**: Godot 4.4.1+
- **平台**: Windows, macOS, Linux
- **内存**: 建议4GB+
- **存储**: 500MB+

## 📋 开发规范

### 代码规范
- 使用snake_case命名变量和函数
- 使用PascalCase命名类和枚举
- 添加类型注解提高性能
- 编写清晰的注释和文档

### 提交规范
- [功能] 新功能开发
- [修复] Bug修复
- [优化] 性能优化
- [文档] 文档更新

## 📚 技术文档

详细的技术文档请参考：
- [开发指南](DEVELOPMENT_GUIDE.md) - 完整的开发文档
- [API文档](docs/api/) - 代码API文档
- [架构设计](docs/architecture/) - 系统架构说明

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见[LICENSE](LICENSE)文件

---

**项目状态**: 🚧 开发中  
**当前版本**: v0.1.0-alpha  
**最后更新**: 2025-01-16
