# Company.gd
# 公司类 - 表示游戏中的公司和组织
class_name Company
extends Resource

# 公司类型枚举
enum CompanyType {
	STARTUP,        # 初创公司
	SMALL_BUSINESS, # 小企业
	CORPORATION,    # 大公司
	GOVERNMENT,     # 政府机构
	NON_PROFIT,     # 非营利组织
	COOPERATIVE     # 合作社
}

# 行业类型枚举
enum Industry {
	TECHNOLOGY,     # 科技
	MANUFACTURING,  # 制造业
	RETAIL,         # 零售
	FINANCE,        # 金融
	HEALTHCARE,     # 医疗
	EDUCATION,      # 教育
	ENTERTAINMENT,  # 娱乐
	AGRICULTURE,    # 农业
	CONSTRUCTION,   # 建筑
	SERVICES        # 服务业
}

# 基础信息
@export var id: String = ""
@export var name: String = ""
@export var company_type: CompanyType = CompanyType.SMALL_BUSINESS
@export var industry: Industry = Industry.SERVICES
@export var founded_date: String = ""

# 财务信息
@export var capital: float = 100000.0      # 资本
@export var revenue: float = 0.0           # 收入
@export var expenses: float = 0.0          # 支出
@export var profit: float = 0.0            # 利润
@export var debt: float = 0.0              # 债务

# 人员信息
@export var employees: Array[String] = []   # 员工NPC IDs
@export var max_employees: int = 10
@export var ceo_id: String = ""            # CEO NPC ID

# 业务信息
@export var products: Array[String] = []   # 产品/服务
@export var market_share: float = 0.0      # 市场份额
@export var reputation: float = 50.0       # 声誉 (0-100)

# 位置信息
@export var headquarters_id: String = ""   # 总部建筑ID
@export var branches: Array[String] = []   # 分支机构

# 状态信息
@export var is_active: bool = true
@export var growth_rate: float = 0.0       # 增长率

func _init():
	"""初始化公司"""
	id = generate_unique_id()
	founded_date = Time.get_datetime_string_from_system()

func generate_unique_id() -> String:
	"""生成唯一ID"""
	return "company_" + str(Time.get_ticks_msec()) + "_" + str(randi())

func hire_employee(npc_id: String) -> bool:
	"""雇佣员工"""
	if employees.size() < max_employees and npc_id not in employees:
		employees.append(npc_id)
		return true
	return false

func fire_employee(npc_id: String) -> bool:
	"""解雇员工"""
	var index = employees.find(npc_id)
	if index >= 0:
		employees.remove_at(index)
		return true
	return false

func calculate_monthly_financials():
	"""计算月度财务"""
	# 简化的财务计算
	var base_revenue = employees.size() * 5000.0  # 每员工5000收入
	var base_expenses = employees.size() * 3000.0  # 每员工3000支出
	
	# 根据行业调整
	match industry:
		Industry.TECHNOLOGY:
			base_revenue *= 1.5
			base_expenses *= 1.2
		Industry.FINANCE:
			base_revenue *= 1.8
			base_expenses *= 1.1
		Industry.RETAIL:
			base_revenue *= 0.8
			base_expenses *= 0.9
	
	revenue = base_revenue * (1.0 + randf_range(-0.2, 0.2))
	expenses = base_expenses * (1.0 + randf_range(-0.1, 0.1))
	profit = revenue - expenses
	
	# 更新资本
	capital += profit

func get_company_type_name() -> String:
	"""获取公司类型名称"""
	match company_type:
		CompanyType.STARTUP:
			return "初创公司"
		CompanyType.SMALL_BUSINESS:
			return "小企业"
		CompanyType.CORPORATION:
			return "大公司"
		CompanyType.GOVERNMENT:
			return "政府机构"
		CompanyType.NON_PROFIT:
			return "非营利组织"
		CompanyType.COOPERATIVE:
			return "合作社"
		_:
			return "未知"

func get_industry_name() -> String:
	"""获取行业名称"""
	match industry:
		Industry.TECHNOLOGY:
			return "科技"
		Industry.MANUFACTURING:
			return "制造业"
		Industry.RETAIL:
			return "零售"
		Industry.FINANCE:
			return "金融"
		Industry.HEALTHCARE:
			return "医疗"
		Industry.EDUCATION:
			return "教育"
		Industry.ENTERTAINMENT:
			return "娱乐"
		Industry.AGRICULTURE:
			return "农业"
		Industry.CONSTRUCTION:
			return "建筑"
		Industry.SERVICES:
			return "服务业"
		_:
			return "未知"

func to_dict() -> Dictionary:
	"""转换为字典格式"""
	return {
		"id": id,
		"name": name,
		"company_type": company_type,
		"industry": industry,
		"capital": capital,
		"revenue": revenue,
		"expenses": expenses,
		"profit": profit,
		"employees": employees.duplicate(),
		"reputation": reputation,
		"is_active": is_active,
		"founded_date": founded_date
	}
