[gd_scene load_steps=3 format=3 uid="uid://bqhqhqhqhqhqh"]

[ext_resource type="Script" path="res://ui/menus/MainMenu.gd" id="1_1a2b3"]
[ext_resource type="Theme" uid="uid://bqhqhqhqhqhqi" path="res://ui/themes/main_theme.tres" id="2_2b3c4"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1a2b3")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.15, 1)

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = 200.0

[node name="Title" type="Label" parent="MainContainer"]
layout_mode = 2
text = "LifeD - 生活模拟沙盒游戏"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="MenuButtons" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
alignment = 1

[node name="NewGameButton" type="Button" parent="MainContainer/MenuButtons"]
layout_mode = 2
text = "新游戏"

[node name="LoadGameButton" type="Button" parent="MainContainer/MenuButtons"]
layout_mode = 2
text = "加载游戏"

[node name="SettingsButton" type="Button" parent="MainContainer/MenuButtons"]
layout_mode = 2
text = "设置"

[node name="QuitButton" type="Button" parent="MainContainer/MenuButtons"]
layout_mode = 2
text = "退出游戏"

[node name="Spacer2" type="Control" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="VersionLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "版本 1.0.0"
horizontal_alignment = 1

[connection signal="pressed" from="MainContainer/MenuButtons/NewGameButton" to="." method="_on_new_game_pressed"]
[connection signal="pressed" from="MainContainer/MenuButtons/LoadGameButton" to="." method="_on_load_game_pressed"]
[connection signal="pressed" from="MainContainer/MenuButtons/SettingsButton" to="." method="_on_settings_pressed"]
[connection signal="pressed" from="MainContainer/MenuButtons/QuitButton" to="." method="_on_quit_pressed"]
