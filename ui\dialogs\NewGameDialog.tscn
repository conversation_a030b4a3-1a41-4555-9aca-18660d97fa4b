[gd_scene load_steps=2 format=3 uid="uid://bqhqhqhqhqhqj"]

[ext_resource type="Script" path="res://ui/dialogs/NewGameDialog.gd" id="1_1a2b4"]

[node name="NewGameDialog" type="AcceptDialog"]
title = "新游戏设置"
initial_position = 2
size = Vector2i(500, 400)
script = ExtResource("1_1a2b4")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.0
offset_top = 8.0
offset_right = -8.0
offset_bottom = -49.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "创建新游戏"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerInfoContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerNameContainer" type="HBoxContainer" parent="VBoxContainer/PlayerInfoContainer"]
layout_mode = 2

[node name="NameLabel" type="Label" parent="VBoxContainer/PlayerInfoContainer/PlayerNameContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "角色姓名:"

[node name="NameLineEdit" type="LineEdit" parent="VBoxContainer/PlayerInfoContainer/PlayerNameContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "玩家"
placeholder_text = "请输入角色姓名"

[node name="PlayerAgeContainer" type="HBoxContainer" parent="VBoxContainer/PlayerInfoContainer"]
layout_mode = 2

[node name="AgeLabel" type="Label" parent="VBoxContainer/PlayerInfoContainer/PlayerAgeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "角色年龄:"

[node name="AgeSpinBox" type="SpinBox" parent="VBoxContainer/PlayerInfoContainer/PlayerAgeContainer"]
layout_mode = 2
min_value = 18.0
max_value = 80.0
value = 25.0

[node name="PlayerProfessionContainer" type="HBoxContainer" parent="VBoxContainer/PlayerInfoContainer"]
layout_mode = 2

[node name="ProfessionLabel" type="Label" parent="VBoxContainer/PlayerInfoContainer/PlayerProfessionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "初始职业:"

[node name="ProfessionOptionButton" type="OptionButton" parent="VBoxContainer/PlayerInfoContainer/PlayerProfessionContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="WorldSettingsContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="WorldSettingsLabel" type="Label" parent="VBoxContainer/WorldSettingsContainer"]
layout_mode = 2
text = "世界设置"
horizontal_alignment = 1

[node name="DifficultyContainer" type="HBoxContainer" parent="VBoxContainer/WorldSettingsContainer"]
layout_mode = 2

[node name="DifficultyLabel" type="Label" parent="VBoxContainer/WorldSettingsContainer/DifficultyContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "游戏难度:"

[node name="DifficultyOptionButton" type="OptionButton" parent="VBoxContainer/WorldSettingsContainer/DifficultyContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EconomySpeedContainer" type="HBoxContainer" parent="VBoxContainer/WorldSettingsContainer"]
layout_mode = 2

[node name="EconomySpeedLabel" type="Label" parent="VBoxContainer/WorldSettingsContainer/EconomySpeedContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "经济速度:"

[node name="EconomySpeedSlider" type="HSlider" parent="VBoxContainer/WorldSettingsContainer/EconomySpeedContainer"]
layout_mode = 2
size_flags_horizontal = 3
min_value = 0.5
max_value = 2.0
step = 0.1
value = 1.0

[node name="EconomySpeedValueLabel" type="Label" parent="VBoxContainer/WorldSettingsContainer/EconomySpeedContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
text = "1.0x"

[node name="Spacer" type="Control" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[connection signal="value_changed" from="VBoxContainer/WorldSettingsContainer/EconomySpeedContainer/EconomySpeedSlider" to="." method="_on_economy_speed_changed"]
